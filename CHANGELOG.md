# Changelog

## Version History

24/10/2024   v1.1    Initial commit: Basic app with buggy voice recognition
15/11/2024   v1.5    Hardcoded agent with state pathways for simple activities (BUGGY)
04/12/2024   v1.7    Reflex agent can change / delete with voice no bugs
31/12/2024   v2.0    New brain interface for agent, concept screen and functionality (buggy)
18/02/2025   v2.2    Bluetooth, bug fixes in concept screen, main screen design improvements
13/03/2025   v2.4    Visually selecting wish item in main screen with voice or UI input
26/03/2025   v2.5    Cancel ongoing brain conversation button. Interrupt button (incomplete)
28/03/2025   v2.5.1  Refined end conversation button on concept screen
01/04/2025   v2.6    Introduced genie avatar image reflecting current state, speaking, listening
01/04/2025   v2.7    Affirmation tool accessible by brain in concept screen
14/04/2025   v2.8    <PERSON><PERSON>'s process screen introduced, but conversation not working!
21/04/2025   v2.9    Core Loop implemented on main screen
11/05/2025   v2.9.2  Core loop with 2 prompt chains (gets stuck in opening questions)
11/05/2025   v2.9.3  Core loop with external metrics tracking
02/06/2025   v2.9.5  Fixed error propagation from BrainService
06/06/2025   v2.9.6  Check-in prompts improved
06/06/2025   v2.9.7  5 minute Check-in timer with +/- controls
13/06/2025   v2.10.0 Transition from check-in
16/06/2025   v2.10.1 UI Components Refactor & Check-In Question Variety Fix
17/06/2025   v2.10.2 Theme Extraction Full Turn Processing
30/06/2025   v2.10.3 Conversation Agent Partial Modularization - split into focused modules
30/06/2025   v2.11.0 TransitionChain upgrade with enhanced two-chain architecture
03/07/2025   v2.11.1 Documentation and project organization improvements
03/07/2025   v2.11.2 StateManager architecture simplification - removed unnecessary wrapper layer
10/07/2025   v2.12.0 Theme persistence implementation with serialization fixes and metadata logging
10/07/2025   v2.13.0 Theme display enhancement with comprehensive debugging infrastructure
11/07/2025   v2.14.0 Theme serialization fix with JsonElement architecture and performance optimization
16/07/2025   v2.15.0 MainScreen UI enhancements and theme architecture refinements
18/07/2025   v2.16.0 Conversational wish creation system development (WIP)



## [2.16.0] - Conversational Wish Creation System (In Development)

### Added
- Conversational wish creation PRD and architecture planning
- WishUtilities extracted to shared business logic module
- TransitionChain modifications for WISH_COLLECTION priority
- Wish creation system module structure planning

### Changed
- Reorganized project planning documents for better readability
- Improved wish slot management utilities for conversational flows
- Updated MVP planning for July release to prioritize conversational wish creation

## [2.15.0] - MainScreen UI Enhancements and Theme Architecture Refinements (2025-07-16)

### Added
- Theme architecture planning documentation (mainscreen_ui_and_theming.md)
- Theme persistence analysis documentation (theme_persistence_and_json.md)

### Changed
- MainScreen UI component organization and structure improvements
- Updated project management workflows and MVP planning documentation
- Enhanced AgentCortex and AgentViewModel state coordination

## [2.14.0] - Theme Serialization Architecture Fix (2025-07-11)

### Fixed
- **Conversation theme persistence bug**: Fixed critical serialization error preventing themes from saving correctly across app sessions
- **JsonElement metadata system**: Eliminated double serialization by implementing proper JsonElement-based data handling
- **Legacy data compatibility**: Added robust error handling to prevent crashes when encountering old malformed conversation data

### Improved
- **App startup performance**: Optimized session loading by reducing unnecessary data processing during app launch
- **Theme display UI**: Enhanced theme card layout with better space utilization and scrollable list for multiple themes
- **Database efficiency**: Added targeted queries to load only required data without triggering expensive metadata parsing

### Added
- **Room TypeConverter for metadata**: Implemented MetadataTypeConverter for seamless JsonElement to String database storage
- **Enhanced error handling**: Added graceful fallback mechanisms for parsing legacy conversation metadata
- **Performance monitoring**: Optimized conversation entry loading to eliminate eager processing of historical data

## [2.13.0] - Theme Cross-Session Persistence (2025-07-10)

### Added
- **Theme persistence**: Themes now persist across sessions and reload correctly in agent panel
- **Theme display**: Agent panel shows conversational themes with count and titles

### Fixed
- **Transition message storage**: Fixed transition messages not being stored in conversation history
- **Theme metadata flow**: Added themes field to TransitionActionPlan for complete persistence
- **JSON serialization**: Fixed double-escaping bug in metadata storage

## [2.12.0] - Theme Persistence Implementation (2025-07-10)

### Added
- **Theme metadata storage**: Conversational themes now stored in conversation entry metadata for persistence across sessions
- **Session naming**: Sessions automatically named based on extracted themes using intelligent TransitionChain analysis
- **Comprehensive metadata logging**: Enhanced logging system tracks theme persistence and metadata storage throughout conversation lifecycle
- **App restart support**: Session names and themes persist correctly across app restarts with robust metadata retrieval

### Fixed
- **List serialization error**: Resolved Json configuration issue in ConversationAgent.kt line 1555 for List<ConversationalTheme> serialization
- **Metadata structure standardization**: Implemented consistent metadata format across all conversation entries with proper theme storage
- **Theme serialization patterns**: Aligned theme serialization with established codebase patterns using configured Json instances

### Changed
- **Enhanced conversation history**: Agent responses now include theme data in metadata for improved session continuity
- **Improved session management**: Sessions are automatically named and organized based on conversational content
- **Metadata architecture**: Conversation entries now support rich metadata including themes, strategy, reasoning, and session context

### Technical Improvements
- **Json configuration consistency**: Applied proper Json configuration (ignoreUnknownKeys, isLenient, coerceInputValues) for complex object serialization
- **Per-entry metadata tracking**: Comprehensive logging and validation of metadata storage for each conversation entry
- **Session context preservation**: Enhanced session data persistence ensuring themes are available when resuming conversations
- **Error handling robustness**: Improved graceful handling of missing or malformed theme metadata

### Architecture Benefits
- **Foundation for MVP features**: Theme persistence enables conversation history review and theme visualization features planned for MVP
- **Conversation continuity**: Users can now resume sessions with full theme context preserved
- **Data integrity**: Robust metadata storage ensures conversational context is never lost
- **Scalable design**: Metadata structure supports future enhancements for theme analysis and user insights

## [2.11.2] - StateManager Architecture Simplification (2025-07-03)

### Changed
- **Removed StateManager wrapper layer**: Eliminated unnecessary abstraction between ConversationAgent and AgentCortex
- **Direct AgentCortex access**: All components now access state directly through AgentCortex methods
- **Simplified architecture**: Reduced complexity by removing 15 wrapper methods that provided no added value
- **Updated CommandMode**: Migrated from StateManager to direct AgentCortex and MainScreenState access

### Technical Improvements
- **Made AgentCortex functions public**: Changed from `internal` to `public` for direct component access
- **Consolidated state management**: Single access pattern instead of mixed StateManager/direct access
- **Reduced maintenance overhead**: Changes now only require updates in AgentCortex, not wrapper layer
- **Improved code clarity**: Direct method calls are more explicit than wrapper delegation

### Architecture Benefits
- **Aligned with design intent**: AgentCortex now serves as the true "prefrontal cortex" central state manager
- **Eliminated architectural confusion**: Single clear access pattern for all state operations
- **Reduced complexity**: Removed unnecessary abstraction layer that added no business logic value

## [2.11.1] - Documentation and Project Organization (2025-07-03)

### Changed
- Updated CHANGELOG.md with comprehensive version history and proper chronological dating
- Added changelog formatting requirements to DEVELOPMENT_WORKFLOW.md
- Reorganized planning documents by moving completed PRDs to `/complete/` folder
- Updated various context documentation files

### Fixed
- Corrected version history dates based on actual git commit timestamps
- Standardized changelog format across all entries

## [2.11.0] - ConversationAgent Modular Refactoring (2025-06-30)

### Added
- **CommandMode class**: Extracted command-driven conversation flows (wish collection/selection) into dedicated class with stateful management
- **ConversationType.CommandMode**: New conversation type for routing command-mode conversations 
- **VoiceCommandEntity.kt relocation**: Moved to voice package for better organization
- **Core Loop Manager planning**: Comprehensive planning document for Core Loop extraction
- **Function-based architecture**: Successfully implemented for voice and command modules

### Changed  
- **ConversationAgent size reduction**: Reduced from ~3,000 to ~2,407 lines through modular extraction
- **Command routing**: Centralized all command processing through CommandMode class
- **State management**: Improved conversation type routing with clear separation between AI-driven and command-driven flows
- **Legacy conversation types**: WishCollection and WishSelection now deprecated in favor of CommandMode

### Technical Improvements
- **Dependency injection**: Clean constructor injection pattern for CommandMode
- **State encapsulation**: Command-specific state variables properly isolated within CommandMode
- **Function organization**: Direct function calls replace complex wrapper patterns
- **Package organization**: voice/ package consolidation for voice-related functionality

### Refactoring Progress
- ✅ **Phase 1-4 Complete**: ConversationUtilities, StateManager, VoiceProcessor, CommandMode extracted
- 🔄 **Phase 5 Planned**: Core Loop Manager extraction planning completed
- 📊 **Metrics**: ~600+ lines extracted to specialized modules

## [2.10.2] - Theme Extraction Full Turn Processing

### Fixed
- Theme extraction now processes all user messages in conversational turn instead of just the last message
- Added proper turn boundary detection using agent responses as delimiters
- Enhanced logging and prompt structure for multi-message processing

### Technical Changes
- Added `getUserMessagesSinceLastAgentResponse()` helper in `DialogueChain.kt`
- Updated `extractThemes()` and `buildThemeExtractionPrompt()` for multiple messages

## [2.10.1] - Check-In Question Variety Fixed

### Fixed
- **Check-in question repetition**: Replaced formal coaching examples with 6 casual, varied starter questions
- **Conversation opener diversity**: Now randomly selects from casual questions like "What's on your mind today?", "What have you been up to recently?", "How are things going for you?"
- **Eliminated formal language**: Removed continuity assumptions and overly formal coaching language from initial questions
- **Forced randomization**: DialogueChain now enforces exact usage of casual starter examples to prevent AI defaulting to formal language

### Technical Changes
- Updated `CoachingTranscripts.kt` CONVERSATION_STARTING examples with casual alternatives
- Modified `DialogueChain.kt` to force random selection of starter questions
- Preserved original formal examples in comments for future reference

## [2.9.7]

### Added
- 5 minute Check-in timer with + - X

### Fixed

- Fix bug with extracting themes
- Improve how themes are selected from 
- Fix bug with not showing enough conversation history


## [2.9.6] - Check-In Prompts significantly improved!

### Added
- Add current wishes to prompts
- HistoryTokenManager to ensure conversation history doesn't max out model's token size: configurable token allowance for conversation history
- HistoryFormatter usable as String output for all prompts requiring history

### Fixed
- Duplication of most recent conversation history entry
- DialogueChain Error Handling back to ConversationAgent
- Replaced state property in ConversationEntry with phase: ConversationPhase to align in-memory data model with database schema
- Improved Prompt Generation: Streamlined LLM prompt construction process by removing unnecessary parameters from DialogueChain and HistoryTokenManager

### Documentation
- New convo_agent_context.md
- All context files up to date



## [2.9.5] - 2/06/25

### Added

- This CHANGELOG.md and VERSION to keep track of versions ongoing.


### Fixed

- Error when attempting to activate GENIE without internet - propagation of errors from BrainService back to ConversationAgent, through DialogueChain.
	* Fixed network error handling by adding try-catch in `handleBrainServiceCall`
	* Resolved UI state synchronization during error conditions
	* Added proper handling for JobCancellationException across the application
- Fixed "stop" command bug during WishSelection by checking both conversation state variables

