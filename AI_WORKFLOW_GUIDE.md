# AI Workflow Guide: VoxManifestorApp Development

## 1. Core Principle: Role-Based AI Usage

To maximize efficiency and leverage the unique strengths of each AI tool, we use a role-based approach. Each AI is assigned a primary responsibility, ensuring we use the right tool for the right job.

## 2. The AI Team & Their Roles

| AI Tool     | Primary Role         | Key Responsibilities                                           |
| :---------- | :------------------- | :------------------------------------------------------------- |
| **Cursor IDE**  | The Implementer      | Real-time coding, debugging, and rapid implementation.         |
| **Claude Code** | MVP Project & Release Manager  | MVP progress tracking, version management, release coordination, file operations.  |
| **Augment**     | The Architect        | Deep, strategic codebase analysis (used sparingly).            |
| **Gemini**      | The AI Specialist    | Designing conversational logic, dialogue flows, and prompts.     |

## 3. Standard Development Workflow

Our development process follows a structured, two-phase workflow. This ensures we have a clear, agreed-upon plan before implementing any changes.

### Phase 1: Planning (The Planner Role)

*   **Goal**: To create a clear, comprehensive, and agreed-upon plan for a given task.
*   **Process**:
    1.  Analyze the user's request and the relevant codebase.
    2.  Create or update a plan in the `@agent/scratchpad.md` file.
    3.  The plan must include a **Background**, **Implementation Plan** (with atomic tasks), and **Success Criteria**.
    4.  Collaborate with the user to refine and approve the plan.
*   **Primary AI Tools**: **Claude Code** (for analysis and planning) and **Cursor IDE** (for writing the plan into the scratchpad).
*   **Strategic AI Tool**: **Augment** may be consulted for tasks requiring deep architectural review.

### Phase 2: Implementation (The Executor Role)

*   **Goal**: To execute the approved plan from the scratchpad accurately and efficiently.
*   **Process**:
    1.  Follow the task breakdown in the scratchpad step-by-step.
    2.  Write and modify code to implement the required changes.
    3.  Report progress, blockers, or completion of each task.
    4.  Update the scratchpad by marking tasks as `[COMPLETED]`.
*   **Primary AI Tool**: **Cursor IDE** (for all active coding).
*   **Supporting AI Tool**: **Claude Code** (for file operations, running tests, and managing commits).

### Phase 3: AI & Dialogue System Implementation

For features involving conversational AI, the workflow is slightly different:

1.  **Design (Planner)**: Use **Gemini** to design the conversation flow, logic, and prompts.
2.  **Implement (Executor)**: Use **Cursor IDE** to translate the Gemini-designed logic into Kotlin code.
3.  **Test & Refine**: Use **Claude Code** to run tests and validate the conversational experience.

## 4. Strategic Guidelines

To get the most value from our powerful, limited-access tools:

*   **Use Augment for Strategy, Not Tactics**: Ask deep architectural questions (e.g., "Analyze the scalability of this data model") before major refactoring, not simple implementation questions.
*   **Use Gemini for AI Logic, Not General Code**: Focus Gemini's expertise on designing prompts, conversation flows, and AI system integration, not on general-purpose Kotlin coding.
