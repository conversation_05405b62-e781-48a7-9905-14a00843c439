# Wish Creation PRD - VoxManifestorApp

## MVP July Launch v1.1 Context & Strategic Focus

### MVP Philosophy & Strategic Focus
- The current phase is a **hyper-focused MVP**: deliver a polished, downloadable app that a real user can use and provide feedback on within two weeks.
- The MVP is not just minimal, but "Minimal Lovable"—it must validate the core loop, foster engagement, and be compelling enough for early adopters.
- All effort is focused on the most critical, user-facing features that demonstrate the app's unique value.

### High-Level MVP Task Breakdown

**Phase 1: Core Conversational Experience**
- **Theme Visualization System**: Display extracted themes in an organized, reviewable format.
- **Conversation Replay & Summarization**: Enable the agent to repeat and summarize themes on request.

**Phase 2: Conversational Wish Management (**Critical Priority**)**
- **Conversational Wish Creation & Editing**: Implement an LLM-driven, fully conversational process for defining, editing, and removing the user's 5 main wishes (goals). This replaces all basic CRUD with guided, natural dialogue.
- **Success Criteria**: User can manage all core wishes entirely through conversation, with no manual editing or command-based flows required.

### Deferred to Post-MVP
- **Concept Manager Extraction**: Modularize concept-building logic (for post-MVP).
- **UI & History Enhancements**: Star Trek-style UI, animated history, goal display upgrades, onboarding, and polish are all deferred.
- **Session Control & Resume**: Advanced session management and onboarding are not required for MVP.

### Minimal Lovable Product (MLP) Philosophy
- The MVP/MLP must:
  - Validate the core mechanics: check-in, wish definition, present/desired state, next step.
  - Demonstrate core utility and engagement.
  - Attract and retain at least one real user for feedback.
  - Provide a stable foundation for rapid iteration and future feature layering.

## High-Level Requirements
- Users can define, edit, and remove up to 5 core wishes entirely through conversation.
- The agent guides users in a natural, supportive way, leveraging LLMs for all wish management flows.
- All wish/slot business logic is extracted to `WishUtilities.kt` and shared between admin and conversational flows.
- The architecture is modular, with clear separation between business logic (utilities), conversational orchestration (WishCreationManager), and UI/state management.

## Success Criteria
- Users can manage all core wishes through conversation, with no manual editing or command-based flows required.
- The system prioritizes wish creation when slots are available, and provides clear, encouraging prompts.
- All logic is robust, testable, and easily extensible for future features (concepts, affirmations, etc.).

## Architectural Overview
- **WishCreationManager**: Implements the "frontal cortex" pattern, orchestrating LLM-driven wish management.
- **WishUtilities.kt**: Contains all stateless wish/slot logic, used by both admin and conversational modules.
- **AgentCortex/ConversationAgent**: Integrates wish creation into the core loop, manages state and transitions.
- **TransitionChain**: Handles phase suggestion and message crafting, now prioritizing wish creation for MVP.


## Implementation Tracking
- **Scratchpad**: The step-by-step working memory for implementation, tracking current status, blockers, and next actions. See `agent/scratchpad.md` for detailed progress and context.
- **Task Pad**: For granular, actionable sub-tasks during implementation. See `agent/taskpad.md` for the current task breakdown.

## Relevant Project Context for Wish Creation & MVP

- **Wishes as Core Loop Foundation:** Wishes are the primary organizing principle of the manifestation process in VoxManifestor. Users define up to five core wishes, which serve as the entry point for all subsequent workflows (present/desired state, pathway planning, affirmations, etc.).

- **Voice-First, Conversational Workflows:** The app is designed to be voice-first, with all major workflows (check-in, wish management, state articulation, next step planning) facilitated through natural, supportive conversation with the agent.

- **MVP Focus:** The MVP centers on a streamlined core loop: Check-In → Wish Definition/Review → Present/Desired State Articulation → Next Step Identification. The goal is to validate these core mechanics and provide a lovable, engaging experience for early users.

- **Modular, Utility-Driven Architecture:** The codebase is organized into modular components (agent, check-in, commands, utilities, etc.), with shared business logic (e.g., wish/slot utilities) extracted for use in both admin and conversational flows. This supports maintainability and extensibility.

- **Iterative, Layered Conversation Planning:** The agent uses a multi-layered approach to conversation planning: (1) filling gaps in the user's wish conceptual map, (2) re-engaging on stale or outdated information, and (3) prioritizing immediate user concerns from check-in. This enables dynamic, context-aware dialogue.

- **User-Friendly, Supportive Language:** All agent interactions should use clear, encouraging, and non-technical language, making the experience feel like a trusted advisor or "genie" guiding the user.

- **Robust State Management & Extensibility:** State is managed centrally (AgentCortex, ViewModels, repositories) using reactive patterns (StateFlow, Flow). The architecture is designed for easy extension as new features (e.g., concept building, advanced affirmation flows) are added post-MVP.

## References
- For detailed architecture, see `agent/context/codebase_context.md` and `agent/context/project_context.md`.
- For theoretical foundation, see `agent/context/manifestation_context.md`.

---

# (Implementation details, step-by-step plans, and granular tasks have been moved to the scratchpad and task pad. This PRD remains the high-level guide for the feature.)