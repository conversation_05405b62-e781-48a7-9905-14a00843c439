# Agent Scratchpad

This file serves as a working memory for planning and documenting ongoing development tasks.

## Current Task: Phase II MVP - Conversational Goal Management

### Background

We are implementing Phase II of the MVP July Launch v1.1, which focuses on **Conversational Goal Management**. This involves replacing the existing hard-coded command system for editing the 5 main wishes with a sophisticated LLM-driven conversational interface.

**Current State:**
- The app has a working Core Loop system that primarily launches the check-in process
- There's an existing command-based goal editing system in `CommandMode.kt` (421 lines) that handles CRUD operations for the 5 wish slots
- The concept screen transition has been deferred to post-MVP to focus on core functionality
- Check-in process successfully transitions users through sophisticated dialogue chains

**Goal:**
Replace the command-driven goal editing with a conversational system that allows users to create, edit, and delete their 5 main life goals entirely through natural dialogue, integrated into the Core Loop flow.

### Analysis / Key Challenges

**1. Architectural Integration Challenges:**
- Need to integrate goal management into the Core Loop flow where concept screen transition currently exists
- Must preserve the sophisticated check-in → transition pattern while redirecting to goal management instead of concept screen
- Need to handle the "stashing" of existing transition logic that currently bridges to concept screen

**2. Data Flow Complexity:**
- Current CommandMode.kt has complex state machine logic for wish slot management (finding empty slots, validation flows, etc.)
- Need to extract the core business logic while replacing the rigid state machine with conversational flow
- Must maintain database operations (ManifestationRepository) and UI state synchronization (MainScreenState)

**3. Conversation Design Challenges:**
- Handle first-time users with empty wish slots vs. returning users with full slots
- Provide natural conversation flow for: creating new wishes, editing existing wishes, deleting wishes to make room
- Maintain user agency in goal selection while providing intelligent suggestions
- Ensure conversation feels natural and not like a command interface

**4. State Management Complexity:**
- Need to track conversation state for goal management (similar to check-in state)
- Must integrate with existing AgentCortex state management patterns
- Handle transitions between goal management and other Core Loop phases

**5. LLM Integration Requirements:**
- Design prompts for goal creation, editing, and deletion conversations
- Implement tool library for goal management operations (similar to concept tools)
- Handle LLM decision-making for when goal management conversation is complete

### Task Breakdown

**[IN PROGRESS] Task 1: Architecture Analysis and Business Logic Extraction**
- Map current CommandMode.kt functionality to identify core business logic for extraction
- Extract shared business logic into reusable utilities that both CommandMode and WishCreationManager can use
- Analyze TransitionChain to ensure wish creation options are available to phase suggestion logic
- Define data structures for wish creation conversation state

**Task 2: Wish Creation Module Creation**
- Create WishCreationManager following "frontal cortex" pattern (processing engine with no external interface access, returns structured results)
- Link to extracted shared business logic utilities from CommandMode.kt
- Implement LLM-driven conversation flow for wish operations
- Create wish creation tool library for database operations

**Task 3: Core Loop Integration**
- Modify transition logic to route to wish creation conversation (not replacing concept screen)
- Implement wish creation conversation state in AgentCortex
- Update ConversationAgent to orchestrate wish creation conversations
- Ensure proper state transitions and conversation scope management

**Task 4: Wish Creation Conversation Design**
- Design prompts for wish creation scenarios (focusing on theme-based wish creation)
- Implement conversation flow for capturing and validating new wishes
- Handle completion and transition back to Core Loop decision-making process
- Consider how wish creation integrates with Core Loop cycle progression

**Task 5: UI Integration and Testing**
- Ensure goal management integrates with existing UI state patterns
- Test conversation flows for different user scenarios
- Validate database operations and state synchronization
- Implement error handling and recovery patterns

**Task 6: Dual System Coexistence**
- Preserve CommandMode.kt for administrative voice commands
- Ensure both conversational and command-based wish editing coexist
- Extract shared business logic so both systems can use the same underlying operations
- Update documentation to clarify the two interaction paradigms (conversational vs. administrative)

### Success Criteria

**Primary Success Criteria:**
1. **Complete Conversational Interface**: Users can create, edit, and delete their 5 main wishes entirely through natural conversation without any command-based interactions
2. **Seamless Core Loop Integration**: Goal management flows naturally from check-in process and transitions appropriately to other Core Loop phases
3. **Intelligent Conversation Management**: System handles empty slots, full slots, and various editing scenarios through natural dialogue
4. **Preserved Functionality**: All existing goal management capabilities (slot management, validation, database operations) are maintained in the new conversational interface

**Technical Success Criteria:**
1. **Modular Architecture**: Goal management follows established patterns (frontal cortex, state management, LLM integration)
2. **State Management**: Proper integration with AgentCortex and conversation scope management
3. **Error Handling**: Robust error handling and conversation recovery patterns
4. **Performance**: Smooth conversation flow without noticeable delays or state inconsistencies

**User Experience Success Criteria:**
1. **Natural Conversation**: Goal management feels like talking to a coach, not operating a command interface
2. **User Agency**: Users maintain control over goal selection and editing decisions
3. **Contextual Awareness**: System remembers conversation context and provides relevant suggestions
4. **Graceful Handling**: System handles user changes of mind, cancellations, and edge cases naturally

## Current Analysis: CommandMode.kt Business Logic Extraction

### Core Business Logic Components

**1. Slot Management Logic:**
- `findNextEmptyWishSlot()` - Identifies available slots for new wishes
- Slot validation and boundary checking (0-4 range, MAX_WISH_SLOTS)
- Slot selection and UI state synchronization via MainScreenState

**2. Wish Lifecycle Operations:**
- Create: Capture wish text → validate → save to database
- Edit: Select existing wish → capture new text → validate → update database
- Delete: Select existing wish → confirm deletion → remove from database
- Validation flow with user confirmation (yes/no responses)

**3. State Machine Patterns:**
- ConversationStep tracking (WishStep, WishSelectionStep)
- Sequential flow management (AskForWish → CaptureWish → CheckWish → CaptureValidation)
- Input type management (FREEFORM, YES_NO, COMMAND_SELECTION)

**4. Database Integration:**
- ManifestationRepository operations (insert, delete, getBySlot, getAll)
- Manifestation entity creation with proper slot assignment
- Existing wish cleanup before slot reuse

### Proposed Architecture: WishCreationManager

Following the established "frontal cortex" pattern used by CoreLoopManager and ConceptManager:

**Design Principles:**
- **Processing Engine**: Handles wish creation logic without external interface access
- **No External Interfaces**: No access to speech, state updates, history, or UI
- **Pure Processing**: Performs LLM integration, business logic, and database operations
- **Result-Based**: Returns structured results for ConversationAgent to act upon
- **ConversationAgent Control**: ConversationAgent retains control of all external interfaces

**"Frontal Cortex" Pattern Explanation:**
Like CoreLoopManager and ConceptManager, this is a processing engine that:
- Receives input data and context
- Performs complex internal processing (LLM calls, business logic, algorithms)
- Returns structured results with instructions for the orchestrator
- Has NO access to external interfaces (speech, UI, state management)
- Allows ConversationAgent to maintain control of all external interactions

**Core Components:**

1. **WishCreationManager** (Main Processing Engine)
   - `processWishCreation(context, userInput)` - Main orchestration method
   - `determineWishAction(context, userInput)` - LLM-driven action selection
   - `executeWishOperation(action, context)` - Business logic execution
   - `createWishPrompt(context, userInput)` - Prompt engineering for LLM

2. **WishCreationContext** (Data Structure)
   - Current wish slots state (empty/filled)
   - User input and conversation history
   - Extracted themes from check-in
   - Operation type (create new wish)

3. **WishCreationResult** (Return Structure)
   - Speech response for user
   - Should continue conversation flag
   - Updated context if changes made
   - Completion action if wish creation finished

4. **WishToolLibrary** (Database Operations)
   - CreateWishTool - Handle new wish creation
   - ValidateWishTool - Handle wish validation
   - Links to shared business logic utilities extracted from CommandMode.kt

**Integration Pattern:**
```
ConversationAgent (Orchestrator)
    ↓
WishCreationManager.processWishCreation(context, userInput)
    ↓
WishCreationResult (instructions for ConversationAgent)
    ↓
ConversationAgent executes result (speak, update state, transition)
```

### Core Loop Integration Strategy

**Current Transition Flow (To Be Modified):**
```
Check-In Process → TransitionChain → Concept Screen Navigation
```

**Proposed New Flow:**
```
Check-In Process → TransitionChain → Goal Management Conversation → Next Core Loop Phase
```

**Implementation Approach:**

1. **Modify TransitionChain Logic:**
   - Update `TransitionChain.processTransition()` to route to goal management instead of concept screen
   - Preserve theme-based transition intelligence but redirect to goal management
   - Maintain existing transition message crafting with goal management context

2. **Add Goal Management State:**
   - Create `GoalManagementState` in AgentCortex (similar to CheckInState)
   - Track current goal operation, selected slot, conversation progress
   - Integrate with existing state management patterns

3. **ConversationAgent Integration:**
   - Add `processGoalManagement()` method to ConversationAgent
   - Handle goal management conversation scope and lifecycle
   - Manage transitions from goal management to other Core Loop phases

4. **Preserve Existing Patterns:**
   - Maintain sophisticated error handling from check-in system
   - Use established conversation scope management
   - Follow existing state update patterns through AgentCortex

**Conversation Flow Design:**

**Scenario 1: Empty Wish Slots Available**
```
Agent: "I noticed you have some empty wish slots. Would you like to add a new wish?"
User: "Yes, I'd like to add something about my career"
Agent: "Great! Tell me about your career wish..."
[Conversation continues until wish is captured and validated]
```

**Scenario 2: All Slots Full - Need to Edit/Replace**
```
Agent: "Your wish list is full. Would you like to edit an existing wish or replace one?"
User: "I want to change my fitness goal"
Agent: "I see your current fitness wish is 'Get in better shape'. How would you like to change it?"
[Conversation continues for editing process]
```

**Scenario 3: User-Initiated Deletion**
```
Agent: "Which wish would you like to remove?"
User: "The one about learning Spanish"
Agent: "I found your Spanish learning wish. Are you sure you want to remove it?"
[Confirmation and deletion process]
```

### Detailed Integration Strategy

**Current Flow Analysis:**
1. **Check-In Process** → `DialogueChain.evaluateTransition()` → `TransitionChain.processTransition()`
2. **TransitionChain** → Creates `TransitionActionPlan` with `proposedPhase` and `targetWishId`
3. **ConversationAgent** → `handleStructuredTransition(actionPlan)` → `navigateCoreLoopPhase(actionPlan)`
4. **navigateCoreLoopPhase** → Routes based on phase:
   - `PRESENT_STATE_EXPLORATION` / `DESIRED_STATE_EXPLORATION` → `launchConceptScreenForPhase()`
   - Other phases → Continue main conversation flow

**Key Integration Points to Modify:**

**1. TransitionChain Phase Selection (Minimal Changes)**
- Current `getValidTransitionPhases()` includes `WISH_COLLECTION`
- Current prompts already handle wish collection scenarios
- **Action**: Update prompts to emphasize goal management over concept exploration for MVP

**2. navigateCoreLoopPhase Routing (Primary Change)**
- Current routing sends concept phases to concept screen
- **Action**: Add `WISH_COLLECTION` routing to wish creation conversation
- **Action**: Keep concept screen routing for post-MVP (don't disable, just add wish creation)

**3. Wish Creation Conversation State**
- **Action**: Add `WishCreationState` to AgentCortex (similar to CheckInState)
- **Action**: Add `ConversationType.WishCreation` for conversation scope management
- **Action**: Implement wish creation conversation loop in ConversationAgent

**Modified navigateCoreLoopPhase Implementation:**
```kotlin
when (targetPhase) {
    ConversationPhase.WISH_COLLECTION -> {
        // NEW: Route to wish creation conversation
        initiateWishCreationConversation(actionPlan)
    }
    ConversationPhase.PRESENT_STATE_EXPLORATION,
    ConversationPhase.DESIRED_STATE_EXPLORATION -> {
        // KEEP EXISTING FOR POST-MVP
        launchConceptScreenForPhase(actionPlan.targetWishId, targetPhase)
    }
    else -> {
        changeDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))
    }
}
```

**New Wish Creation Integration:**
```kotlin
private suspend fun initiateWishCreationConversation(actionPlan: TransitionActionPlan) {
    // 1. Update conversation type
    agentCortex.updateConversationType(ConversationType.WishCreation)

    // 2. Initialize wish creation state
    val wishContext = createWishCreationContext(actionPlan)
    agentCortex.updateWishCreationState(wishContext)

    // 3. Start wish creation conversation
    processWishCreation(wishContext, userInput = null)
}

private suspend fun processWishCreation(context: WishCreationContext, userInput: String?) {
    val result = wishCreationManager.processWishCreation(context, userInput)

    // Agent handles all external interfaces
    speak(result.speechResponse)
    addToHistory(Speaker.Agent, result.speechResponse, agentCortex.coreLoopState.value.currentPhase)

    if (result.shouldContinue) {
        changeDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))
    } else {
        // Wish creation complete - return to Core Loop decision-making
        handleWishCreationCompletion(result)
    }
}
```

### Data Structures Design

**WishCreationContext** (Input to WishCreationManager):
```kotlin
data class WishCreationContext(
    val currentWishes: List<WishSummary>,           // Current wish slots state
    val availableSlots: List<Int>,                  // Empty slot indices
    val conversationHistory: List<ConversationEntry>, // Recent conversation context
    val extractedThemes: List<ConversationalTheme>, // Themes from check-in
    val transitionReasoning: String,                // Why we're creating wishes
    val targetSlot: Int? = null,                    // Preferred slot for new wish
    val themeContext: String? = null                // Specific theme being addressed
)
```

**WishCreationResult** (Output from WishCreationManager):
```kotlin
data class WishCreationResult(
    val speechResponse: String,                     // Text for agent to speak
    val shouldContinue: Boolean,                    // Continue wish creation conversation
    val updatedContext: WishCreationContext?,      // Updated context if changes made
    val completionAction: WishCreationCompletionAction? = null, // Action when complete
    val databaseOperations: List<WishOperation> = emptyList() // Operations to execute
)

enum class WishCreationCompletionAction {
    RETURN_TO_CORE_LOOP_DECISION,  // Return to Core Loop for next phase decision
    CREATE_ANOTHER_WISH,           // User wants to create another wish
    END_SESSION                    // End the conversation session
}

sealed class WishOperation {
    data class CreateWish(val slot: Int, val title: String) : WishOperation()
    data class ValidateWish(val slot: Int, val title: String) : WishOperation()
}
```

**WishCreationState** (AgentCortex State):
```kotlin
data class WishCreationState(
    val isActive: Boolean = false,
    val currentContext: WishCreationContext? = null,
    val conversationStage: WishCreationStage = WishCreationStage.THEME_EXPLORATION,
    val pendingOperations: List<WishOperation> = emptyList()
)

enum class WishCreationStage {
    THEME_EXPLORATION,     // Exploring the theme that led to wish creation
    CONTENT_CAPTURE,       // Capturing wish content
    VALIDATION,            // Confirming the new wish
    COMPLETION            // Wrapping up wish creation
}
```

### WishCreationManager Implementation Strategy

**Core Processing Method:**
```kotlin
suspend fun processWishCreation(
    context: WishCreationContext,
    userInput: String?
): WishCreationResult {

    // 1. Determine current conversation stage
    val stage = determineConversationStage(context, userInput)

    // 2. Generate appropriate prompt for LLM
    val prompt = buildWishCreationPrompt(context, userInput, stage)

    // 3. Get LLM decision
    val brainDecision = brainService.getWishCreationDecision(prompt)

    // 4. Execute any required tools/operations using shared business logic
    val operations = executeWishOperations(brainDecision, context)

    // 5. Return structured result
    return WishCreationResult(
        speechResponse = brainDecision.responseText,
        shouldContinue = !brainDecision.isComplete,
        updatedContext = updateContext(context, brainDecision, operations),
        databaseOperations = operations
    )
}
```

### Conversation Flow Design

**Stage 1: Initial Assessment**
- Analyze available slots and user themes
- Determine if user wants to create, edit, or delete
- Handle first-time users vs. returning users

**Stage 2: Slot/Wish Selection**
- For creation: Find best available slot
- For editing: Help user identify which wish to modify
- For deletion: Confirm which wish to remove

**Stage 3: Content Capture**
- For creation: Capture new wish description
- For editing: Capture updated wish description
- Natural conversation flow, not rigid prompts

**Stage 4: Validation**
- Confirm the operation with user
- Handle user changes of mind
- Provide clear summary of what will happen

**Stage 5: Completion**
- Execute database operations
- Provide confirmation to user
- Determine next steps (continue Core Loop, end session, etc.)

### Prompt Engineering Strategy

**Goal Management Prompts:**
```kotlin
private fun buildGoalManagementPrompt(
    context: GoalManagementContext,
    userInput: String?,
    stage: GoalManagementStage
): String {
    return when (stage) {
        GoalManagementStage.INITIAL_ASSESSMENT -> buildAssessmentPrompt(context)
        GoalManagementStage.SLOT_SELECTION -> buildSelectionPrompt(context, userInput)
        GoalManagementStage.CONTENT_CAPTURE -> buildCapturePrompt(context, userInput)
        GoalManagementStage.VALIDATION -> buildValidationPrompt(context, userInput)
        GoalManagementStage.COMPLETION -> buildCompletionPrompt(context)
    }
}
```

**Example Assessment Prompt:**
```
GOAL MANAGEMENT CONVERSATION

You are helping the user manage their 5 main life goals. Based on the check-in conversation,
you need to guide them through creating, editing, or deleting their wishes.

CURRENT SITUATION:
- Available slots: [1, 3, 5] (slots 2 and 4 are filled)
- Existing wishes:
  * Slot 2: "Get in better shape"
  * Slot 4: "Learn Spanish"

THEMES FROM CHECK-IN:
- Work stress and career dissatisfaction
- Desire for better work-life balance
- Interest in creative pursuits

CONVERSATION CONTEXT:
[Recent conversation history]

Your task: Determine what the user wants to do with their goals. Options:
1. Create a new wish (you have 3 empty slots available)
2. Edit an existing wish
3. Delete an existing wish to make room for something new

Respond naturally and conversationally. Ask what feels most important to them right now.
```

## Next Steps for Implementation

**Immediate Actions (Task 1 Completion):**

1. **Extract Shared Business Logic from CommandMode.kt**
   - Create `utilities/WishBusinessLogic.kt` for shared operations
   - Extract slot management logic (`findNextEmptyWishSlot`, slot validation)
   - Extract database operation patterns (create, update, delete wishes)
   - Extract validation flows and confirmation patterns
   - Update CommandMode.kt to use extracted utilities

2. **Analyze TransitionChain for Wish Creation Integration**
   - Review `getValidTransitionPhases()` to ensure WISH_COLLECTION is properly supported
   - Examine phase suggestion prompts to ensure wish creation scenarios are covered
   - Verify that TransitionChain can route to wish creation appropriately

3. **Create Wish Creation Module Structure**
   - Create `wishcreation/` directory under `ui/agent/`
   - Create `WishCreationManager.kt` following frontal cortex pattern
   - Create `WishCreationSystem.kt` for data structures
   - Create `WishToolLibrary.kt` linking to shared business logic

4. **Update AgentCortex State Management**
   - Add `WishCreationState` to AgentCortex
   - Add `updateWishCreationState()` method
   - Add `ConversationType.WishCreation` enum value

5. **Modify ConversationAgent Integration**
   - Add `initiateWishCreationConversation()` method
   - Add `processWishCreation()` method
   - Update `navigateCoreLoopPhase()` routing for WISH_COLLECTION
   - Add wish creation to `processUserResponse()` handling

6. **Update BrainService**
   - Add `getWishCreationDecision()` method
   - Create wish creation prompt templates focusing on theme-based wish creation
   - Add response parsing for wish creation decisions

This completes the architectural analysis and design phase. The next step would be to begin implementation of the GoalManagementManager module.