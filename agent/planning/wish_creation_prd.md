# VoxManifestorApp - Wish Creation PRD

## Current Task: Phase II MVP - Conversational Goal Management

### Background

We are implementing Phase II of the MVP July Launch v1.1, which focuses on **Conversational Goal Management**. This involves replacing the existing hard-coded command system for editing the 5 main wishes with a sophisticated LLM-driven conversational interface.

**Current State:**
- The app has a working Core Loop system that primarily launches the check-in process
- There's an existing command-based goal editing system in `CommandMode.kt` (421 lines) that handles CRUD operations for the 5 wish slots
- The concept screen transition has been deferred to post-MVP to focus on core functionality
- Check-in process successfully transitions users through sophisticated dialogue chains
- **✅ COMPLETED**: Shared wish/slot business logic extracted to WishUtilities.kt

**Goal:**
Replace the command-driven goal editing with a conversational system that allows users to create, edit, and delete their 5 main life goals entirely through natural dialogue, integrated into the Core Loop flow.

### MVP Philosophy & Strategic Focus
- The current phase is a **hyper-focused MVP**: deliver a polished, downloadable app that a real user can use and provide feedback on within two weeks.
- The MVP is not just minimal, but "Minimal Lovable"—it must validate the core loop, foster engagement, and be compelling enough for early adopters.
- All effort is focused on the most critical, user-facing features that demonstrate the app's unique value.

### Deferred to Post-MVP
- **Concept Manager Extraction**: Modularize concept-building logic (for post-MVP).
- **UI & History Enhancements**: Star Trek-style UI, animated history, goal display upgrades, onboarding, and polish are all deferred.
- **Session Control & Resume**: Advanced session management and onboarding are not required for MVP.

### Minimal Lovable Product (MLP) Philosophy
- The MVP/MLP must:
  - Validate the core mechanics: check-in, wish definition, present/desired state, next step.
  - Demonstrate core utility and engagement.
  - Attract and retain at least one real user for feedback.
  - Provide a stable foundation for rapid iteration and future feature layering.

### Analysis / Key Challenges

**1. Architectural Integration Challenges:**
- Need to integrate goal management into the Core Loop flow where concept screen transition currently exists
- Must preserve the sophisticated check-in → transition pattern while redirecting to goal management instead of concept screen
- Need to handle the "stashing" of existing transition logic that currently bridges to concept screen

**2. Data Flow Complexity:**
- Current CommandMode.kt has complex state machine logic for wish slot management (finding empty slots, validation flows, etc.)
- Need to extract the core business logic while replacing the rigid state machine with conversational flow
- Must maintain database operations (ManifestationRepository) and UI state synchronization (MainScreenState)

**3. Conversation Design Challenges:**
- Handle first-time users with empty wish slots vs. returning users with full slots
- Provide natural conversation flow for: creating new wishes, editing existing wishes, deleting wishes to make room
- Maintain user agency in goal selection while providing intelligent suggestions
- Ensure conversation feels natural and not like a command interface

**4. State Management Complexity:**
- Need to track conversation state for goal management (similar to check-in state)
- Must integrate with existing AgentCortex state management patterns
- Handle transitions between goal management and other Core Loop phases

**5. LLM Integration Requirements:**
- Design prompts for goal creation, editing, and deletion conversations
- Implement tool library for goal management operations (similar to concept tools)
- Handle LLM decision-making for when goal management conversation is complete

## High-Level Requirements
- Users can define, edit, and remove up to 5 core wishes entirely through conversation.
- The agent guides users in a natural, supportive way, leveraging LLMs for all wish management flows.
- All wish/slot business logic is extracted to `WishUtilities.kt` and shared between admin and conversational flows.
- The architecture is modular, with clear separation between business logic (utilities), conversational orchestration (WishCreationManager), and UI/state management.

## Success Criteria

**Primary Success Criteria:**
1. **Complete Conversational Interface**: Users can create, edit, and delete their 5 main wishes entirely through natural conversation without any command-based interactions
2. **Seamless Core Loop Integration**: Goal management flows naturally from check-in process and transitions appropriately to other Core Loop phases
3. **Intelligent Conversation Management**: System handles empty slots, full slots, and various editing scenarios through natural dialogue
4. **Preserved Functionality**: All existing goal management capabilities (slot management, validation, database operations) are maintained in the new conversational interface

**Technical Success Criteria:**
1. **Modular Architecture**: Goal management follows established patterns (frontal cortex, state management, LLM integration)
2. **State Management**: Proper integration with AgentCortex and conversation scope management
3. **Error Handling**: Robust error handling and conversation recovery patterns
4. **Performance**: Smooth conversation flow without noticeable delays or state inconsistencies

**User Experience Success Criteria:**
1. **Natural Conversation**: Goal management feels like talking to a coach, not operating a command interface
2. **User Agency**: Users maintain control over goal selection and editing decisions
3. **Contextual Awareness**: System remembers conversation context and provides relevant suggestions
4. **Graceful Handling**: System handles user changes of mind, cancellations, and edge cases naturally


## Current Implementation Status

### Completed Components ✅

**1. TransitionChain Analysis and Backup [COMPLETED]**
- ✅ Original concept-focused prompt logic preserved in commented backup methods
- ✅ Current active prompts analyzed for modification requirements
- ✅ WISH_COLLECTION phase already supported in `getValidTransitionPhases()`
- ✅ Ready for MVP-focused prompt modifications

**2. Architecture Planning [COMPLETED]**
- ✅ Detailed architectural design documented in wish_creation_prd.md
- ✅ "Frontal cortex" pattern defined for WishCreationManager
- ✅ Data structures designed (WishCreationContext, WishCreationResult, WishCreationState)
- ✅ Integration patterns with ConversationAgent planned

**3. Shared Wish/Slot Business Logic Extraction [COMPLETED]**
- ✅ Shared wish/slot business logic extracted to WishUtilities.kt
- ✅ CommandMode.kt refactored to use WishUtilities.kt utilities
- ✅ Utilities now support both admin and conversational flows

### Missing Components ❌

**1. Wish Creation Module Structure [NOT CREATED]**
- ❌ No `wishcreation/` directory exists under `ui/agent/`
- ❌ WishCreationManager.kt not implemented
- ❌ WishCreationSystem.kt not created
- ❌ WishToolLibrary.kt not implemented

**2. AgentCortex State Management [PARTIAL]**
- ❌ WishCreationState not added to AgentCortex
- ❌ ConversationType.WishCreation enum value not added
- ❌ updateWishCreationState() method not implemented

**3. ConversationAgent Integration [NOT IMPLEMENTED]**
- ❌ initiateWishCreationConversation() method not added
- ❌ processWishCreation() method not implemented
- ❌ navigateCoreLoopPhase() routing for WISH_COLLECTION not updated
- ❌ processUserResponse() handling for wish creation not added

**4. BrainService Integration [NOT IMPLEMENTED]**
- ❌ getWishCreationDecision() method not added
- ❌ Wish creation prompt templates not created
- ❌ Response parsing for wish creation decisions not implemented

**5. TransitionChain MVP Modifications [READY TO START]**
- ❌ Current prompts still emphasize concept exploration over wish creation
- ❌ No slot availability analysis in decision-making logic
- ❌ Generic transition messaging doesn't emphasize wish creation benefits

## Task Breakdown

**[READY TO START] Task 1: Complete TransitionChain MVP Modifications**
- Update buildPhaseSuggestionPrompt() to prioritize wish creation when slots available
- Add slot availability analysis to decision-making logic
- Update buildMessageCraftingPrompt() with wish creation-focused messaging
- Preserve concept screen logic in backup methods (already done)

**Task 2: Wish Creation Module Creation**
- Create WishCreationManager following "frontal cortex" pattern (processing engine with no external interface access, returns structured results)
- Link to extracted shared business logic utilities from WishUtilities.kt
- Implement LLM-driven conversation flow for wish operations
- Create wish creation tool library for database operations

**Task 3: Core Loop Integration**
- Modify transition logic to route to wish creation conversation (not replacing concept screen)
- Implement wish creation conversation state in AgentCortex
- Update ConversationAgent to orchestrate wish creation conversations
- Ensure proper state transitions and conversation scope management

**Task 4: Wish Creation Conversation Design**
- Design prompts for wish creation scenarios (focusing on theme-based wish creation)
- Implement conversation flow for capturing and validating new wishes
- Handle completion and transition back to Core Loop decision-making process
- Consider how wish creation integrates with Core Loop cycle progression

**Task 5: UI Integration and Testing**
- Ensure goal management integrates with existing UI state patterns
- Test conversation flows for different user scenarios
- Validate database operations and state synchronization
- Implement error handling and recovery patterns

**Task 6: Dual System Coexistence**
- Preserve CommandMode.kt for administrative voice commands
- Ensure both conversational and command-based wish editing coexist
- Extract shared business logic so both systems can use the same underlying operations
- Update documentation to clarify the two interaction paradigms (conversational vs. administrative)

## Implementation Tracking
- **Scratchpad**: The step-by-step working memory for implementation, tracking current status, blockers, and next actions. See `agent/scratchpad.md` for detailed progress and context.
- **Task Pad**: For granular, actionable sub-tasks during implementation. See `agent/taskpad.md` for the current task breakdown.

## Architectural Overview

### Proposed Architecture: WishCreationManager

Following the established "frontal cortex" pattern used by CoreLoopManager and ConceptManager:

**Design Principles:**
- **Processing Engine**: Handles wish creation logic without external interface access
- **No External Interfaces**: No access to speech, state updates, history, or UI
- **Pure Processing**: Performs LLM integration, business logic, and database operations
- **Result-Based**: Returns structured results for ConversationAgent to act upon
- **ConversationAgent Control**: ConversationAgent retains control of all external interfaces

**Core Components:**

1. **WishCreationManager** (Main Processing Engine)
   - `processWishCreation(context, userInput)` - Main orchestration method
   - `determineWishAction(context, userInput)` - LLM-driven action selection
   - `executeWishOperation(action, context)` - Business logic execution
   - `createWishPrompt(context, userInput)` - Prompt engineering for LLM

2. **WishCreationContext** (Data Structure)
   - Current wish slots state (empty/filled)
   - User input and conversation history
   - Extracted themes from check-in
   - Operation type (create new wish)

3. **WishCreationResult** (Return Structure)
   - Speech response for user
   - Should continue conversation flag
   - Updated context if changes made
   - Completion action if wish creation finished

4. **WishToolLibrary** (Database Operations)
   - CreateWishTool - Handle new wish creation
   - ValidateWishTool - Handle wish validation
   - Links to shared business logic utilities extracted from WishUtilities.kt

**Integration Pattern:**
```
ConversationAgent (Orchestrator)
    ↓
WishCreationManager.processWishCreation(context, userInput)
    ↓
WishCreationResult (instructions for ConversationAgent)
    ↓
ConversationAgent executes result (speak, update state, transition)
```

### Core Loop Integration Strategy

**Current Transition Flow (To Be Modified):**
```
Check-In Process → TransitionChain → Concept Screen Navigation
```

**Proposed New Flow:**
```
Check-In Process → TransitionChain → Goal Management Conversation → Next Core Loop Phase
```

**Implementation Approach:**

1. **Modify TransitionChain Logic:**
   - Update `TransitionChain.processTransition()` to route to goal management instead of concept screen
   - Preserve theme-based transition intelligence but redirect to goal management
   - Maintain existing transition message crafting with goal management context

2. **Add Goal Management State:**
   - Create `WishCreationState` in AgentCortex (similar to CheckInState)
   - Track current goal operation, selected slot, conversation progress
   - Integrate with existing state management patterns

3. **ConversationAgent Integration:**
   - Add `processWishCreation()` method to ConversationAgent
   - Handle goal management conversation scope and lifecycle
   - Manage transitions from goal management to other Core Loop phases

4. **Preserve Existing Patterns:**
   - Maintain sophisticated error handling from check-in system
   - Use established conversation scope management
   - Follow existing state update patterns through AgentCortex

## Relevant Project Context for Wish Creation & MVP

- **Wishes as Core Loop Foundation:** Wishes are the primary organizing principle of the manifestation process in VoxManifestor. Users define up to five core wishes, which serve as the entry point for all subsequent workflows (present/desired state, pathway planning, affirmations, etc.).

- **Voice-First, Conversational Workflows:** The app is designed to be voice-first, with all major workflows (check-in, wish management, state articulation, next step planning) facilitated through natural, supportive conversation with the agent.

- **MVP Focus:** The MVP centers on a streamlined core loop: Check-In → Wish Definition/Review → Present/Desired State Articulation → Next Step Identification. The goal is to validate these core mechanics and provide a lovable, engaging experience for early users.

- **Modular, Utility-Driven Architecture:** The codebase is organized into modular components (agent, check-in, commands, utilities, etc.), with shared business logic (e.g., wish/slot utilities) extracted for use in both admin and conversational flows. This supports maintainability and extensibility.

- **Iterative, Layered Conversation Planning:** The agent uses a multi-layered approach to conversation planning: (1) filling gaps in the user's wish conceptual map, (2) re-engaging on stale or outdated information, and (3) prioritizing immediate user concerns from check-in. This enables dynamic, context-aware dialogue.

- **User-Friendly, Supportive Language:** All agent interactions should use clear, encouraging, and non-technical language, making the experience feel like a trusted advisor or "genie" guiding the user.

- **Robust State Management & Extensibility:** State is managed centrally (AgentCortex, ViewModels, repositories) using reactive patterns (StateFlow, Flow). The architecture is designed for easy extension as new features (e.g., concept building, advanced affirmation flows) are added post-MVP.

## References
- For detailed architecture, see `agent/context/codebase_context.md` and `agent/context/project_context.md`.
- For theoretical foundation, see `agent/context/manifestation_context.md`.
- For current implementation status and next steps, see `agent/scratchpad.md`
- For granular task breakdown, see `agent/taskpad.md`

---