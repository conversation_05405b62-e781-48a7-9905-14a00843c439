# 3-Minute Timer PRD

## Overview
A 3-minute timer for the CHECK_IN phase, displayed in a mode status bar below the Agent toolbar. Provides visual feedback and controls for session duration.

## Implementation Phases

### Phase 1: Core Timer Module [COMPLETED]
* **Objective**: Create the foundational timer module with state management
* **Completed**: Implemented TimerState, TimerIntent, and CheckInTimerManager with proper state management

### Phase 2: AgentCortex Integration [COMPLETED]
* **Objective**: Integrate timer state management into AgentCortex
* **Completed**: Added timer state to CheckInState and implemented UI intents

### Phase 3: ConversationAgent Integration [COMPLETED]
* **Objective**: Integrate timer with conversation flow
* **Completed**: Added timer initialization and expiration handling

### Phase 4: UI Implementation [COMPLETED]
* **Objective**: Create user interface for timer display and control
* **Completed**: Implemented ModeStatusBar with timer controls and state management

### Phase 5: Transition Logic [COMPLETED]
* **Objective**: Update dialogue chain to handle timer-based transitions
* **Completed**: Implemented timer-first transition evaluation and ensured the timer is stopped when the CHECK_IN phase completes naturally.

### Phase 6: Polish and Testing [IN PROGRESS]
* **Objective**: Enhance user experience and ensure reliability
* **Current**: Implementing haptic feedback and animations

## Success Criteria

### Functional Requirements
- [x] Timer starts correctly and transitions smoothly after expiration
- [ ] Timer controls (add time, stop) work reliably
- [x] State persists correctly across app lifecycle events
- [x] Timer stops when conversation transitions out of Check-in phase

### User Experience
- [x] Clear visual feedback of remaining time and timer state
- [ ] Intuitive and responsive timer controls
- [ ] Smooth transitions between timer states

### Technical Validation
- [x] Timer accuracy within ±3 seconds
- [ ] No memory leaks or state inconsistencies
- [ ] Proper cleanup and resource management

## Notes
- Timer duration (5 minutes) should be easily configurable for testing
- Consider adding haptic feedback for timer controls
- Ensure proper cleanup during app lifecycle events

# Check-In Timer System

## Current Architecture


## Current Flow

### UI Layer (MainScreen.kt)
1. Timer UI Components:
   - `ModeStatusBar` composable shows timer controls
   - Timer state accessed from `CheckInState.timerState`
   - Timer controls call `viewModel.handleTimerIntent(TimerIntent.*)`

### ViewModel Layer
1. MainViewModel.kt:
   - Delegates timer intents to AgentViewModel
   - `handleTimerIntent(intent: TimerIntent)` forwards to `agentViewModel.handleTimerIntent(intent)`

2. AgentViewModel.kt:
   - Receives timer intents from MainViewModel
   - Plays button sound
   - Forwards intent to AgentCortex via `submitIntent(AgentCortex.UiIntent.TimerControl(intent))`

### State Management (AgentCortex.kt)
1. Timer State Storage:
   - Maintains `CheckInState` flow with timer state
   - Updates timer state through `updateTimerState` method

2. Intent Handling:
   - Receives `TimerControl` intents through `UiIntent` sealed class
   - Forwards to ConversationAgent for processing

### Routing (ConversationAgent.kt)
1. Conversation agent monitors Ui Intents from AgentCortex
2. Routes timer intent onward to...

### Timer Management (CheckInTimerManager.kt)
1. Core Functionality:
   - Manages timer lifecycle (start, stop, add/subtract time)
   - Updates timer state through AgentCortex
   - Runs background timer monitoring
   - Handles thread safety

2. State Updates:
   - Uses AgentCortex as single source of truth
   - Updates `CheckInState.timerState` through `updateCheckInState`
   - Manages timer job lifecycle

## Notes
- Timer state belongs in `CheckInState`
- All timer controls go through `AgentCortex`
- UI observes `CheckInState` for timer updates
- Need to handle app lifecycle events properly

# Timer System Structure

## 1. State Management

### TimerState (timer/TimerState.kt)
```kotlin
sealed class TimerState {
    data class Active(val remainingTimeMillis: Long) : TimerState()
    object Expired : TimerState()
    object Inactive : TimerState()
    
    companion object {
        const val DEFAULT_DURATION_MS = 300_000L  // 5 minutes
        const val MIN_DURATION_MS = 60_000L       // 1 minute
        const val MAX_DURATION_MS = 600_000L      // 10 minutes
        const val UPDATE_INTERVAL_MS = 1000L      // 1 second
    }
}
```

### CheckInState (checkin/CheckInState.kt)
```kotlin
data class CheckInState(
    val timerState: TimerState = TimerState.Inactive,
    ... <other CheckInState items>
)
```

## 2. Timer Management

### CheckInTimerManager (timer/CheckInTimerManager.kt)
```kotlin
class CheckInTimerManager(
    private val agentCortex: AgentCortex,
    private val coroutineScope: CoroutineScope
) {
    private var timerJob: Job? = null

    fun start() {
        // Start timer with DEFAULT_DURATION_MS
        // Launch background watcher
    }

    fun stop() {
        // Cancel timer job
        // Clean up resources
    }

    fun addTime() {
        // Add MIN_DURATION_MS to active timer
        // Cap at MAX_DURATION_MS
    }

    fun forceTransition() {
        // Set timer to Expired
        // Stop timer
    }

    private fun startTimerWatcher() {
        // Monitor timer state
        // Update remaining time
        // Handle expiration
    }
}
```

## 3. State Flow

```
UI Layer (ModeStatusBar)
    ↓ observes
CheckInState.timerState
    ↓ updated by
CheckInTimerManager
    ↓ manages
TimerState & Background Monitoring
```


## 4. Timer Lifecycle

1. **Initialization**
   - Timer starts when first user response received
   - Initial duration: 5 minutes (DEFAULT_DURATION_MS)

2. **Active State**
   - Background watcher updates every second
   - UI displays remaining time
   - Controls enabled for time adjustment

3. **Expiration**
   - Timer sets Expired state
   - Triggers transition evaluation
   - UI shows expired state

4. **Cleanup**
   - Timer job cancelled
   - State reset to Inactive
   - Resources released


## 5. UI Requirements

### ModeStatusBar
- Display current mode ("Check-In")
- Show countdown timer (MM:SS format)
- Control buttons:
  - [+] Add 1 minute
  - [-] Minus 1 minute
  - [Stop] Force transition
- Visual feedback for state changes

### Timer Display
- Active: Show remaining time
- Expired: Show "00:00"
- Inactive: Show "--:--"
