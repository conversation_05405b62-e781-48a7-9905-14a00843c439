# Concept System Architecture: End-State Documentation

## Overview

This document describes the final architecture of the VoxManifestor Concept System after the ConceptManager extraction. The solution follows the established "frontal cortex" pattern used by CoreLoopManager, creating a clean separation between processing logic and orchestration while preserving all existing integration patterns.

## 1. Architectural Principles

### 1.1. Design Philosophy
The ConceptManager extraction follows these core principles:

- **Incremental Modularization**: Extract processing logic without disrupting working integration patterns
- **Frontal Cortex Pattern**: Processing engine with no external interface access, returns structured results
- **Preserve Orchestration**: ConversationAgent maintains control of all external interfaces (TTS, navigation, state)
- **Respect Existing Patterns**: Maintain successful communication and state management patterns

### 1.2. Problem Resolution
The solution addresses the original concerns while avoiding the risks identified in the analysis:

**✅ Modularization Achieved**: Concept processing logic extracted to dedicated manager
**✅ Maintainability Improved**: Clear separation of concerns with focused responsibilities  
**✅ Integration Preserved**: All existing communication patterns maintained
**✅ Risk Minimized**: Incremental approach with thorough validation at each step

## 2. System Architecture Diagrams

### 2.1. High-Level Component Interaction

```
┌─────────────────────────────────────────────────────────────────┐
│                     CORE LOOP SYSTEM                           │
│  ┌─────────────────┐    ┌─────────────────┐                   │
│  │ CheckIn Phase   │    │ Other Phases    │                   │
│  │ (DialogueChain) │    │ (CoreLoopMgr)   │                   │
│  └─────────────────┘    └─────────────────┘                   │
│                                   │                            │
│                                   ▼                            │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │            ConversationAgent                            │   │
│  │              (Orchestrator)                             │   │
│  │                                                         │   │
│  │  • Controls external interfaces (TTS, Navigation)      │   │
│  │  • Manages conversation scope and lifecycle            │   │
│  │  • Orchestrates Core Loop phase transitions            │   │
│  │  • Integrates with ConceptViewModel via observation    │   │
│  └─────────────────┬───────────────────────────────────────┘   │
└─────────────────────┼───────────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                  CONCEPT SYSTEM                                 │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                ConceptViewModel                         │   │
│  │                                                         │   │
│  │  • Manages concept screen state and data loading       │   │
│  │  • Provides ConceptBuildingContext to agent            │   │
│  │  • Handles UI state and user interactions              │   │
│  │  • Communicates via existing UiIntent system           │   │
│  └─────────────────┬───────────────────────────────────────┘   │
│                    │                                           │
│                    ▼                                           │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │              ConceptManager                             │   │
│  │            (Processing Engine)                          │   │
│  │                                                         │   │
│  │  • Processes concept building logic internally         │   │
│  │  • Calls BrainService and ConceptRepository directly   │   │
│  │  • Executes tool library and prompt engineering        │   │
│  │  • Returns structured results (no external interfaces) │   │
│  └─────────────────┬───────────────────────────────────────┘   │
│                    │                                           │
│                    ▼                                           │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │         External Dependencies                           │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │   │
│  │  │BrainService │  │ConceptRepo  │  │ToolLibrary  │     │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2. Data Flow Architecture

```
Core Loop Phase Transition:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ CHECK_IN        │───▶│ PRESENT_STATE   │───▶│ DESIRED_STATE   │
│ (DialogueChain) │    │ _EXPLORATION    │    │ _EXPLORATION    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────────────────────────────┐
                       │    ConversationAgent.launchConcept     │
                       │         ScreenForPhase()               │
                       │                                         │
                       │  • Sets ConversationType.ConceptBuild  │
                       │  • Navigates to ConceptScreen          │
                       │  • Preserves Core Loop context         │
                       └─────────────────┬───────────────────────┘
                                         │
                                         ▼
                       ┌─────────────────────────────────────────┐
                       │     ConceptViewModel Integration        │
                       │                                         │
                       │  • observeConceptViewModel()            │
                       │  • Monitor DataLoadingState.Ready      │
                       │  • getConceptBuildingContext()         │
                       └─────────────────┬───────────────────────┘
                                         │
                                         ▼
                       ┌─────────────────────────────────────────┐
                       │      Concept Processing Loop            │
                       │                                         │
                       │  ConversationAgent.getNextBrainDecision│
                       │              ↓                         │
                       │  ConceptManager.processConceptAction   │
                       │              ↓                         │
                       │  ConceptProcessingResult               │
                       │              ↓                         │
                       │  Agent speaks & manages state          │
                       └─────────────────┬───────────────────────┘
                                         │
                                         ▼
                       ┌─────────────────────────────────────────┐
                       │         Exit & Return                   │
                       │                                         │
                       │  • UiIntent.NotifyConceptScreenExit     │
                       │  • onConceptScreenExit()                │
                       │  • Navigate back to MainScreen          │
                       │  • Continue Core Loop progression       │
                       └─────────────────────────────────────────┘
```

### 2.3. Detailed Program Cycles

#### Cycle 1: First-Time Concept Screen Launch

**Scenario**: Core Loop transitions to `PRESENT_STATE_EXPLORATION` for the first time

```
1. Core Loop Phase Transition:
   ┌─────────────────────────────────────────────────────────────┐
   │ TransitionChain determines PRESENT_STATE_EXPLORATION needed │
   │ Returns: TransitionActionPlan(                              │
   │   proposedPhase = "PRESENT_STATE_EXPLORATION",              │
   │   targetWishId = 2,                                         │
   │   actionSuggestion = "Let's explore your current work..."   │
   │ )                                                           │
   └─────────────────────────────────────────────────────────────┘
                                    ↓
2. ConversationAgent Routing:
   ┌─────────────────────────────────────────────────────────────┐
   │ handleStructuredTransition(actionPlan)                      │
   │   → speak(actionSuggestion)                                 │
   │   → launchConceptScreenForPhase(wishId=2, PRESENT_STATE)    │
   │   → agentCortex.updateConversationType(ConceptBuilding)     │
   │   → navigationManager.navigateToConceptScreen(wishId=2)     │
   └─────────────────────────────────────────────────────────────┘
                                    ↓
3. ConceptViewModel Initialization:
   ┌─────────────────────────────────────────────────────────────┐
   │ ConceptViewModel(manifestationId=2) created                 │
   │   → loadManifestationDetails().join()                      │
   │   → loadConcepts().join()                                  │
   │   → _dataLoadingState.value = DataLoadingState.Ready       │
   └─────────────────────────────────────────────────────────────┘
                                    ↓
4. Agent-ViewModel Integration:
   ┌─────────────────────────────────────────────────────────────┐
   │ ConversationAgent.observeConceptViewModel(viewModel)        │
   │   → Monitors dataLoadingState                               │
   │   → When Ready: calls initiateConceptBuilding()            │
   │   → Sets up bidirectional communication                    │
   └─────────────────────────────────────────────────────────────┘
                                    ↓
5. Initial Concept Processing:
   ┌─────────────────────────────────────────────────────────────┐
   │ ConversationAgent.initiateConceptBuilding()                 │
   │   → context = viewModel.getConceptBuildingContext()        │
   │   → processConceptInteraction(context, userInput=null)     │
   │   → ConceptManager.processConceptAction(context, null)     │
   │   → Returns: ConceptProcessingResult(                      │
   │       speechResponse = "Tell me about your current work...",│
   │       shouldContinue = true                                │
   │     )                                                      │
   │   → speak(result.speechResponse)                           │
   │   → changeDialogueState(ExpectingInput(BRAIN_RESPONSE))    │
   └─────────────────────────────────────────────────────────────┘
```

#### Cycle 2: User Voice Input Accumulation (CORRECTED)

**Scenario**: How voice input is actually processed in the current system

```
ACTUAL CURRENT FLOW (Following Check-In Pattern):

1. User Speaks (Each Utterance Processed Immediately):
   ┌─────────────────────────────────────────────────────────────┐
   │ VoiceRecognitionRepository captures: "Well, my current job" │
   │   → Triggers processUserResponse() immediately              │
   │   → ConversationAgent.handleUserResponse(text)             │
   │   → addToHistory(Speaker.User, text, currentPhase)         │
   │   → Text added to conversation history in AgentCortex      │
   │   → NO agent response yet - accumulating in history        │
   └─────────────────────────────────────────────────────────────┘
                                    ↓
2. User Continues Speaking (Another Utterance):
   ┌─────────────────────────────────────────────────────────────┐
   │ VoiceRecognitionRepository captures: "is really stressful"  │
   │   → Triggers processUserResponse() again                   │
   │   → ConversationAgent.handleUserResponse(text)             │
   │   → addToHistory(Speaker.User, "is really stressful", phase)│
   │   → Second entry added to conversation history             │
   │   → History now contains multiple user entries             │
   └─────────────────────────────────────────────────────────────┘
                                    ↓
3. User Adds More Detail (Third Utterance):
   ┌─────────────────────────────────────────────────────────────┐
   │ VoiceRecognitionRepository captures: "and my boss doesn't  │
   │ listen to my ideas"                                         │
   │   → Triggers processUserResponse() again                   │
   │   → ConversationAgent.handleUserResponse(text)             │
   │   → addToHistory(Speaker.User, "and my boss doesn't listen │
   │     to my ideas", phase)                                   │
   │   → Third entry added to conversation history              │
   │   → All utterances now in AgentCortex.conversationHistory  │
   └─────────────────────────────────────────────────────────────┘
```

**Key Insight**: Each voice utterance is immediately processed and added to conversation history. The response button doesn't accumulate text - it triggers the agent's response to the accumulated conversation history.

#### Cycle 3: Response Button Processing

**Scenario**: User presses response button to trigger agent's response

```
ACTUAL CURRENT SYSTEM (Check-In Pattern):
1. Response Button Pressed:
   ┌─────────────────────────────────────────────────────────────┐
   │ AgentViewModel.sendResponse()                               │
   │   → agentCortex.submitIntent(UiIntent.SendResponse)         │
   │   → ConversationAgent.monitorUiIntents() receives intent   │
   │   → conversationScope.launch { processUserResponse() }     │
   └─────────────────────────────────────────────────────────────┘
                                    ↓
2. Current Processing Path:
   ┌─────────────────────────────────────────────────────────────┐
   │ ConversationAgent.processUserResponse()                     │
   │   → rawText = processUserResponse(voiceManager, agentCortex)│
   │   → When ConversationType.ConceptBuilding:                 │
   │       → currentConceptViewModel.getConceptBuildingContext()│
   │       → handleUserResponse(rawText) // Adds FINAL to history│
   │       → getNextBrainDecision(context, toolLibrary)         │
   └─────────────────────────────────────────────────────────────┘

KEY INSIGHT: The response button does NOT submit accumulated text. Instead:
- User utterances are already in conversation history (added immediately)
- Response button gets rawSpeechText.value (current/final utterance)
- This final utterance is added to history
- Agent then processes ALL conversation history to generate response

PROPOSED ALIGNMENT (Following Check-In Pattern More Closely):
1. Response Button Pressed:
   ┌─────────────────────────────────────────────────────────────┐
   │ AgentViewModel.sendResponse()                               │
   │   → agentCortex.submitIntent(UiIntent.SendResponse)         │
   │   → ConversationAgent.monitorUiIntents() receives intent   │
   └─────────────────────────────────────────────────────────────┘
                                    ↓
2. Enhanced Processing Path:
   ┌─────────────────────────────────────────────────────────────┐
   │ ConversationAgent.processUserResponse()                     │
   │   → When ConversationType.ConceptBuilding:                 │
   │       → context = currentConceptViewModel.getConceptBuildingContext()│
   │       → Enhanced context includes conversationHistory      │
   │       → processConceptInteraction(context, null)           │
   │       → ConceptManager uses full conversation history      │
   │       → speak(result.speechResponse)                       │
   │       → addToHistory(Speaker.Agent, result.speechResponse) │
   └─────────────────────────────────────────────────────────────┘
```

#### Cycle 4: ConceptManager Processing Deep Dive

**Scenario**: How ConceptManager processes user input and generates responses

```
PROPOSED ConceptManager.processConceptAction() Flow:
1. Input Processing:
   ┌─────────────────────────────────────────────────────────────┐
   │ processConceptAction(context, userInput)                    │
   │   → buildConceptPrompt(context, userInput)                 │
   │   → Prompt includes:                                       │
   │     • Current concept state (Present/Desired items)       │
   │     • User's latest input                                  │
   │     • Conversation history (last few turns)               │
   │     • Phase-specific guidance                              │
   └─────────────────────────────────────────────────────────────┘
                                    ↓
2. BrainService Integration:
   ┌─────────────────────────────────────────────────────────────┐
   │ generateConceptResponse(context, userInput)                 │
   │   → brainService.getNextConceptAction(prompt)              │
   │   → Returns BrainDecision with:                            │
   │     • responseText: "That sounds challenging. Can you..."  │
   │     • toolName: "QuestionTool"                             │
   │     • parameters: {...}                                    │
   │     • isComplete: false                                    │
   └─────────────────────────────────────────────────────────────┘
                                    ↓
3. Tool Execution:
   ┌─────────────────────────────────────────────────────────────┐
   │ executeConceptTools(brainDecision)                          │
   │   → toolLibrary = createConceptToolLibrary()               │
   │   → toolResult = toolLibrary.executeTool(                  │
   │       toolName = "QuestionTool",                           │
   │       parameters = brainDecision.parameters                │
   │     )                                                      │
   │   → May update ConceptRepository if tool saves data       │
   └─────────────────────────────────────────────────────────────┘
                                    ↓
4. Result Generation:
   ┌─────────────────────────────────────────────────────────────┐
   │ Return ConceptProcessingResult(                             │
   │   speechResponse = brainDecision.responseText,             │
   │   shouldContinue = !brainDecision.isComplete,              │
   │   updatedContext = getUpdatedContext(),                    │
   │   toolExecutionResults = listOf(toolResult)               │
   │ )                                                          │
   └─────────────────────────────────────────────────────────────┘
```

#### Cycle 5: Conversation History Integration

**Scenario**: How conversation history is maintained following Check-In pattern

```
CURRENT CHALLENGE:
┌─────────────────────────────────────────────────────────────────┐
│ Concept screen conversation history may not follow the same     │
│ pattern as Check-In system, which uses:                        │
│   • addToHistory(Speaker.User, input, currentPhase)            │
│   • addToHistory(Speaker.Agent, response, currentPhase)        │
│   • Conversation entries stored with phase context             │
│   • History available for LLM context in subsequent turns      │
└─────────────────────────────────────────────────────────────────┘

PROPOSED SOLUTION (Align with Check-In Pattern):
1. User Input History:
   ┌─────────────────────────────────────────────────────────────┐
   │ ConversationAgent.handleConceptResponse()                   │
   │   → userInput = agentCortex.rawSpeechText.value            │
   │   → addToHistory(                                          │
   │       speaker = Speaker.User,                              │
   │       content = userInput,                                 │
   │       phase = coreLoopState.value.currentPhase            │
   │     )                                                      │
   └─────────────────────────────────────────────────────────────┘
                                    ↓
2. Agent Response History:
   ┌─────────────────────────────────────────────────────────────┐
   │ After ConceptManager.processConceptAction():                │
   │   → addToHistory(                                          │
   │       speaker = Speaker.Agent,                             │
   │       content = result.speechResponse,                     │
   │       phase = coreLoopState.value.currentPhase            │
   │     )                                                      │
   │   → History now available for next turn's context         │
   └─────────────────────────────────────────────────────────────┘
                                    ↓
3. History Context for ConceptManager:
   ┌─────────────────────────────────────────────────────────────┐
   │ buildConceptPrompt(context, userInput):                     │
   │   → recentHistory = getRecentConversationHistory()         │
   │   → Include last 3-5 turns for context                     │
   │   → Format similar to Check-In system:                     │
   │     "User: Well, my current job is really stressful"      │
   │     "Agent: That sounds challenging. Can you tell me..."   │
   │     "User: [current input]"                                │
   └─────────────────────────────────────────────────────────────┘
```

### 2.4. Integration Gaps and Proposed Solutions

#### Gap 1: Response Button Processing
**Current Issue**: Response button processing may not align perfectly with Check-In pattern for conversation history usage.

**Current Reality**: The concept system already follows the Check-In pattern:
- User utterances are immediately added to conversation history via `handleUserResponse()`
- Response button triggers agent processing of accumulated conversation history
- The pattern is consistent with Check-In system

**Proposed Enhancement**:
```kotlin
// Enhanced ConceptBuildingContext to include conversation history
data class ConceptBuildingContext(
    val manifestationTitle: String,
    val conceptData: Map<ConceptType, List<ConceptItem>>,
    val includeMetaData: Boolean,
    val conversationHistory: List<ConversationEntry> = emptyList() // NEW
)

// In ConversationAgent - enhanced concept processing
private suspend fun processUserResponse() {
    when (currentConversation) {
        ConversationType.ConceptBuilding -> {
            currentConceptViewModel?.getConceptBuildingContext()?.let { context ->
                // Get recent conversation history for context
                val recentHistory = agentCortex.conversationHistory.value.takeLast(10)
                val enhancedContext = context.copy(conversationHistory = recentHistory)

                // Process with full conversation context
                processConceptInteraction(enhancedContext, null)
            }
        }
    }
}

// Update UiIntent handling to route concept responses correctly
when (intent) {
    is AgentCortex.UiIntent.SendResponse -> {
        when (agentCortex.conversationType.value) {
            ConversationType.CoreLoop -> handleCoreLoopResponse(rawText)
            ConversationType.ConceptBuilding -> handleConceptResponse() // NEW
            else -> logStatus("Unhandled conversation type for SendResponse")
        }
    }
}
```

#### Gap 2: Conversation History Context
**Current Issue**: ConceptManager may not have access to conversation history for context.

**Proposed Solution**:
```kotlin
// Enhanced ConceptBuildingContext to include conversation history
data class ConceptBuildingContext(
    val manifestationTitle: String,
    val conceptData: Map<ConceptType, List<ConceptItem>>,
    val includeMetaData: Boolean,
    val conversationHistory: List<ConversationEntry> = emptyList() // NEW
)

// ConceptManager uses history for better context
private fun buildConceptPrompt(context: ConceptBuildingContext, userInput: String?): String {
    val historyContext = context.conversationHistory
        .takeLast(5) // Last 5 turns for context
        .joinToString("\n") { "${it.speaker}: ${it.content}" }

    return """
        Current Concept Building Context:
        Manifestation: ${context.manifestationTitle}

        Recent Conversation:
        $historyContext
        ${userInput?.let { "User: $it" } ?: ""}

        Current Concept State:
        ${formatConceptData(context.conceptData)}

        Please continue the conversation to help build this concept...
    """.trimIndent()
}
```

## 3. Component Responsibilities

### 3.1. ConversationAgent (Orchestrator)
**Role**: Maintains supreme control over external interfaces and conversation lifecycle

**Responsibilities**:
- **External Interface Control**: Sole owner of TTS, navigation, and conversation scope
- **Core Loop Integration**: Manages phase transitions and context preservation
- **ConceptViewModel Integration**: Observes ViewModel state and provides context bridge
- **State Orchestration**: Updates AgentCortex state and manages conversation history
- **Error Handling**: Centralized error management and conversation recovery

**Key Methods**:
```kotlin
// Existing integration patterns (preserved)
fun observeConceptViewModel(viewModel: ConceptViewModel)
fun launchConceptScreenForPhase(wishId: Int?, phase: ConversationPhase)
fun onConceptScreenExit()

// Updated concept processing delegation (renamed from getNextBrainDecision)
private suspend fun processConceptInteraction(
    context: ConceptBuildingContext,
    userInput: String? = null
) {
    val result = conceptManager.processConceptAction(context, userInput)

    // Agent handles all external interfaces
    speak(result.speechResponse)
    addToHistory(Speaker.Agent, result.speechResponse, agentCortex.coreLoopState.value.currentPhase)

    if (result.shouldContinue) {
        changeDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))
    } else {
        handleConceptPhaseCompletion(result)
    }
}
```

### 3.2. ConceptManager (Processing Engine)
**Role**: Dedicated processor for concept building logic following "frontal cortex" pattern

**Responsibilities**:
- **Internal Processing**: All concept building logic, prompt engineering, tool execution
- **BrainService Integration**: Direct calls to LLM for concept-related decisions
- **ConceptRepository Access**: Direct data persistence and retrieval operations
- **Tool Library Management**: Execution of concept building tools and workflows
- **Result Generation**: Returns structured results with no external interface access

**Key Methods**:
```kotlin
class ConceptManager(
    private val conceptRepository: ConceptRepository,
    private val brainService: BrainService
) {
    suspend fun processConceptAction(
        context: ConceptBuildingContext,
        userInput: String? = null
    ): ConceptProcessingResult

    // Internal processing methods (renamed for clarity)
    private suspend fun generateConceptResponse(
        context: ConceptBuildingContext,
        userInput: String?
    ): BrainDecision

    private fun createConceptToolLibrary(): ConceptToolLibrary
    private fun buildConceptPrompt(context: ConceptBuildingContext, userInput: String?): String
    private suspend fun executeConceptTools(decision: BrainDecision): ToolExecutionResult
}
```

### 3.3. ConceptViewModel (Screen Controller)
**Role**: Manages concept screen state and provides data bridge to agent

**Responsibilities** (Unchanged):
- **Data Loading**: Manages manifestation and concept data loading states
- **Context Provision**: Provides ConceptBuildingContext to ConversationAgent
- **UI State Management**: Handles concept screen UI state and user interactions
- **Agent Communication**: Uses existing UiIntent system for agent communication

**Integration Points** (Preserved):
```kotlin
// Existing methods maintained
fun getConceptBuildingContext(): ConceptBuildingContext?
override fun onCleared() // Sends UiIntent.NotifyConceptScreenExit

// Existing agent integration preserved
val dataLoadingState: StateFlow<DataLoadingState>
fun sendResponse() = agentViewModel.sendResponse()
```

## 4. Data Structures and Communication

### 4.1. Core Data Types

**ConceptProcessingResult** (New):
```kotlin
data class ConceptProcessingResult(
    val speechResponse: String,                    // Text for agent to speak
    val shouldContinue: Boolean,                   // Continue conversation or complete
    val updatedContext: ConceptBuildingContext?,   // Updated context if changes made
    val completionAction: String? = null,          // Action to take on completion
    val toolExecutionResults: List<ToolResult> = emptyList() // Tool execution outcomes
)
```

**ConceptBuildingContext** (Existing, Preserved):
```kotlin
data class ConceptBuildingContext(
    val manifestationTitle: String,
    val conceptData: Map<ConceptType, List<ConceptItem>>,
    val includeMetaData: Boolean
)
```

### 4.2. Communication Patterns

**Agent ↔ ConceptManager**:
```kotlin
// ConversationAgent delegates processing to ConceptManager
val result = conceptManager.processConceptAction(context, toolLibrary)

// ConceptManager returns structured result
return ConceptProcessingResult(
    speechResponse = "Tell me more about your current situation...",
    shouldContinue = true,
    updatedContext = updatedContext
)
```

**Agent ↔ ConceptViewModel** (Preserved):
```kotlin
// Existing observation pattern maintained
observeConceptViewModel(viewModel)
val context = viewModel.getConceptBuildingContext()

// Existing UiIntent communication preserved
UiIntent.SendResponse → Agent processes user input
UiIntent.NotifyConceptScreenExit → Agent handles cleanup
```

**ConceptManager ↔ External Services**:
```kotlin
// Direct service access (no external interfaces)
val brainDecision = brainService.getNextConceptAction(prompt)
conceptRepository.saveConcept(manifestationId, updatedConcept)
val toolResult = toolLibrary.executeTool(decision.toolName, decision.parameters)
```

## 5. Integration with Core Loop System

### 5.1. Phase-Based Concept Integration

The concept system integrates seamlessly with the Core Loop phases:

**PRESENT_STATE_EXPLORATION**:
```kotlin
// Core Loop determines need for present state exploration
TransitionActionPlan(proposedPhase = "PRESENT_STATE_EXPLORATION", targetWishId = 2)

// ConversationAgent routes to concept system
launchConceptScreenForPhase(wishId = 2, phase = PRESENT_STATE_EXPLORATION)

// ConceptManager processes with phase-specific context
conceptManager.processConceptAction(context.withPhase(PRESENT_STATE_EXPLORATION))
```

**DESIRED_STATE_EXPLORATION**:
```kotlin
// Similar pattern for desired state exploration
launchConceptScreenForPhase(wishId = 2, phase = DESIRED_STATE_EXPLORATION)
conceptManager.processConceptAction(context.withPhase(DESIRED_STATE_EXPLORATION))
```

### 5.2. Context Preservation

**Core Loop Context Maintained**:
- `CoreLoopState.currentPhase` preserved during concept screen navigation
- `CoreLoopState.currentWishIndex` maintained for proper routing
- Conversation history and themes preserved across screen transitions
- Return navigation restores exact Core Loop state

**State Synchronization**:
```kotlin
// Before concept screen navigation
val coreLoopSnapshot = agentCortex.coreLoopState.value

// During concept building
// ConceptManager processes without affecting Core Loop state

// After concept screen exit
// Core Loop state restored and progression continues
progressCoreLoop() // Continues from preserved state
```

## 6. Error Handling and Recovery

### 6.1. Layered Error Management

**ConceptManager Level**:
```kotlin
suspend fun processConceptAction(
    context: ConceptBuildingContext,
    toolLibrary: ConceptToolLibrary
): ConceptProcessingResult {
    return try {
        val brainDecision = brainService.getNextConceptAction(buildPrompt(context))
        val toolResult = executeTools(brainDecision)

        ConceptProcessingResult(
            speechResponse = brainDecision.response,
            shouldContinue = !brainDecision.isComplete
        )
    } catch (e: Exception) {
        // Return error result, don't throw
        ConceptProcessingResult(
            speechResponse = "I'm having trouble processing that. Let me try a different approach.",
            shouldContinue = true
        )
    }
}
```

**ConversationAgent Level** (Existing, Enhanced):
```kotlin
private suspend fun processConceptInteraction(
    context: ConceptBuildingContext,
    userInput: String? = null
) {
    try {
        val result = conceptManager.processConceptAction(context, userInput)

        // Agent handles all external interfaces
        speak(result.speechResponse)
        addToHistory(Speaker.Agent, result.speechResponse, agentCortex.coreLoopState.value.currentPhase)

        if (result.shouldContinue) {
            changeDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))
        } else {
            handleConceptPhaseCompletion(result)
        }
    } catch (e: Exception) {
        handleError(e) // Existing centralized error handling
    }
}
```

### 6.2. Conversation Scope Management

**Scope Preservation**:
- ConceptManager operates within existing `conversationScope`
- Cancellation propagates correctly through processing chain
- Clean termination on scope cancellation or errors
- State recovery maintains conversation continuity

## 7. Testing Strategy

### 7.1. Unit Testing

**ConceptManager Testing**:
```kotlin
class ConceptManagerTest {
    @Test
    fun `processConceptAction returns valid result for present state exploration`()

    @Test
    fun `processConceptAction handles BrainService errors gracefully`()

    @Test
    fun `processConceptAction executes tools correctly`()
}
```

**Integration Testing**:
```kotlin
class ConceptSystemIntegrationTest {
    @Test
    fun `Core Loop to Concept Screen transition preserves state`()

    @Test
    fun `Concept building conversation maintains functionality`()

    @Test
    fun `Concept Screen exit returns to correct Core Loop phase`()
}
```

### 7.2. Validation Scenarios

**Critical Test Cases**:
1. **Core Loop Integration**: CHECK_IN → PRESENT_STATE_EXPLORATION → Concept Screen → Return
2. **State Preservation**: Navigation maintains all conversation context and history
3. **Error Recovery**: BrainService failures don't break conversation flow
4. **Scope Management**: Conversation termination cleanly cancels all processes
5. **Data Persistence**: Concept changes are properly saved and reflected in UI

## 8. Benefits and Outcomes

### 8.1. Achieved Goals

**✅ Modularization**: Concept processing logic extracted to dedicated manager
**✅ Maintainability**: Clear separation of concerns with focused responsibilities
**✅ Testability**: ConceptManager can be unit tested independently
**✅ Reusability**: Processing logic can be reused across different contexts
**✅ Consistency**: Follows established architectural patterns (CoreLoopManager)

### 8.2. Preserved Strengths

**✅ Integration Patterns**: All existing communication patterns maintained
**✅ State Management**: AgentCortex remains single source of truth
**✅ Core Loop Flow**: Seamless integration with Core Loop phases
**✅ Error Handling**: Centralized error management preserved
**✅ User Experience**: Zero functional regressions in concept building

### 8.3. Risk Mitigation Success

**✅ Low Implementation Risk**: Incremental extraction with validation at each step
**✅ Preserved Working Systems**: No disruption to sophisticated existing integration
**✅ Maintainable Architecture**: Clear boundaries and responsibilities
**✅ Future Extensibility**: Foundation for additional concept-related features

This architecture provides a solid foundation for the concept system that respects existing patterns while achieving the desired modularization goals safely and effectively.
