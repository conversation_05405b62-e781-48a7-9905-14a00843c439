# Task Pad: Step 1 - MVP-Focused TransitionChain Prompt Modifications

## Current Status (Updated from scratchpad.md)
- ✅ **COMPLETED**: TransitionChain analysis and backup methods created
- ✅ **COMPLETED**: Architecture planning and data structure design
- ✅ **COMPLETED**: Shared wish/slot business logic extracted to WishUtilities.kt
- ✅ **COMPLETED**: CommandMode.kt refactored to use WishUtilities.kt utilities
- 🔄 **IN PROGRESS**: Step 1 - MVP-Focused Prompt Modifications

## Current Task: Step 1 - Implement MVP-Focused Prompt Modifications

**Objective:**
Update TransitionChain prompts to prioritize wish creation for <PERSON> while preserving concept screen logic for post-MVP restoration.

**Key Files & Modules Involved:**
- TransitionChain.kt (prompt logic)
- agent/planning/wish_creation_prd.md (PRD)
- agent/scratchpad.md (current status and context)

## Implementation Sub-Steps / Checklist:

### Task 1.1: Update buildPhaseSuggestionPrompt() Method
- [ ] Import WishUtilities functions in TransitionChain.kt (findNextEmptyWishSlot, isValidWishSlot)
- [ ] Add simple slot availability check using findNextEmptyWishSlot()
- [ ] Modify WISH_COLLECTION criteria to prioritize wish creation for MVP when slots are available
- [ ] Integrate slot availability into decision-making logic
- [ ] Test slot analysis with different wish slot configurations

**Implementation Plan:**
```kotlin
// Add to TransitionChain.kt imports:
import com.example.voxmanifestorapp.ui.agent.utilities.findNextEmptyWishSlot
import com.example.voxmanifestorapp.ui.agent.utilities.isValidWishSlot

// Simple slot availability check in buildPhaseSuggestionPrompt():
val manifestations = enhancedWishes.map { 
    Manifestation(id = it.id, slot = 0, title = it.title) 
}
val firstEmptySlot = findNextEmptyWishSlot(manifestations)
val slotAvailability = if (firstEmptySlot == -1) {
    "All wish slots are filled"
} else {
    "Wish slots are available (next slot: $firstEmptySlot)"
}

// Add to prompt context:
SLOT STATUS: $slotAvailability

// Modify WISH_COLLECTION criteria:
1. **WISH_COLLECTION** - Use when (MVP PRIORITY):
   - Wish slots are available (prioritize this for MVP)
   - Themes suggest new focus areas not covered by existing wishes
   - User expresses new aspirations or goals not related to current wishes
   - No existing wishes, or themes don't relate to current wishes
   - IMPORTANT: For MVP, strongly prefer wish creation when slots are available
   - Set targetWishId: null
```

### Task 1.2: Update buildMessageCraftingPrompt() Method  
- [ ] Add wish creation-specific messaging templates
- [ ] Include slot availability context in transition messages
- [ ] Emphasize benefits and opportunities of creating new wishes
- [ ] Use user-friendly, MVP-appropriate language

### Task 1.3: Integration and Testing
- [ ] Ensure slot availability is calculated and included in prompts
- [ ] Update decision criteria to prioritize wish creation for MVP
- [ ] Preserve concept screen logic in backup methods (already done)
- [ ] Test prompt modifications with various scenarios

## Code Snippets & Notes (Working Memory):

**Slot Availability Analysis Function:**
```kotlin
private fun analyzeSlotAvailability(enhancedWishes: List<EnhancedWishSummary>): String {
    val totalSlots = 5 // MAX_WISH_SLOTS
    val filledSlots = enhancedWishes.size
    val availableSlots = totalSlots - filledSlots

    return when {
        availableSlots == 0 -> "All 5 wish slots are filled"
        availableSlots == 1 -> "1 wish slot available"
        else -> "$availableSlots wish slots available"
    }
}
```

**MVP-Focused WISH_COLLECTION Criteria:**
```
1. **WISH_COLLECTION** - Use when (MVP PRIORITY):
   - User has empty wish slots available (prioritize this for MVP)
   - Themes suggest new focus areas not covered by existing wishes
   - User expresses new aspirations or goals not related to current wishes
   - No existing wishes, or themes don't relate to current wishes
   - IMPORTANT: For MVP, strongly prefer wish creation when slots are available
   - Set targetWishId: null
```

## Blockers / Questions for Planner/User:
- None at this time - ready to proceed with implementation

## Relationship to PRDs and Main Scratchpad:
- This task is derived from wish_creation_prd.md and the architectural plan in agent/scratchpad.md
- Completion of this task will enable Step 2: Create Wish Creation Module Structure
- Current status tracked in agent/scratchpad.md under "Current Task: Complete MVP-Focused TransitionChain Modifications"

---

## Next Steps After Completion:
- Step 2: Create Wish Creation Module Structure (wishcreation/ directory, WishCreationManager.kt, etc.)
- Step 3: AgentCortex State Management Integration
- Step 4: ConversationAgent Integration
- Step 5: BrainService Integration
