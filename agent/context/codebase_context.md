# VoxManifestorApp Codebase Context

## Table of Contents
1. [Directory and File Structure Mapping](#directory-and-file-structure-mapping)
2. [Key Entry Points and Main Flows](#key-entry-points-and-main-flows)
3. [Core Modules and Their Responsibilities](#core-modules-and-their-responsibilities)
4. [Architecture Patterns & Data Flow](#architecture-patterns--data-flow)
5. [Major Data Models and State Management](#major-data-models-and-state-management)
6. [External Dependencies and Integrations](#external-dependencies-and-integrations)
7. [Notable Patterns, Conventions, and Anti-Patterns](#notable-patterns-conventions-and-anti-patterns)
8. [Development Guidelines](#development-guidelines)
9. [Check-In System Integration](#check-in-system-integration)

---

## Directory and File Structure Mapping

```
/ (project root)
├── app/
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/example/voxmanifestorapp/
│   │   │   │   ├── ui/
│   │   │   │   │   ├── agent/                    # Core dialog system logic and components
│   │   │   │   │   │   ├── affirmations/         # Affirmation-specific UI and logic
│   │   │   │   │   │   │   ├── AffirmationManager.kt (affirmation orchestration)
│   │   │   │   │   │   │   ├── AffirmationOverlay.kt (UI overlay components)
│   │   │   │   │   │   │   └── AffirmationTool.kt (affirmation processing logic)
│   │   │   │   │   │   ├── checkin/              # Check-in specific dialogue system
│   │   │   │   │   │   │   ├── CheckInSystem.kt (243 lines) - Canonical data types
│   │   │   │   │   │   │   ├── DialogueChain.kt (723 lines) - 4-chain dialogue architecture
│   │   │   │   │   │   │   ├── TransitionChain.kt (451 lines) - Transition processing
│   │   │   │   │   │   │   ├── CoachingTranscripts.kt (701 lines) - Strategy examples
│   │   │   │   │   │   │   └── README.md, CLAUDE.md (documentation)
│   │   │   │   │   │   ├── commands/             # Command processing and stateful flows
│   │   │   │   │   │   │   ├── CommandFunctions.kt (277 lines) - Discrete command handlers
│   │   │   │   │   │   │   ├── CommandMode.kt (420 lines) - Stateful conversation flows
│   │   │   │   │   │   │   └── README.md (documentation)
│   │   │   │   │   │   ├── components/           # Decomposed UI components
│   │   │   │   │   │   │   ├── AgentDisplays.kt (display utilities)
│   │   │   │   │   │   │   ├── AgentPanels.kt (404 lines) - Panel components
│   │   │   │   │   │   │   ├── AgentProcessPanel.kt (457 lines) - Process visualization
│   │   │   │   │   │   │   ├── AgentToolbar.kt (toolbar components)
│   │   │   │   │   │   │   ├── AgentUtils.kt (utility functions)
│   │   │   │   │   │   │   ├── CheckInTimer.kt (timer UI components)
│   │   │   │   │   │   │   ├── ModeStatusBar.kt (status display)
│   │   │   │   │   │   │   └── README.md (documentation)
│   │   │   │   │   │   ├── coreloop/             # Core loop management
│   │   │   │   │   │   │   └── CoreLoopManager.kt (core loop processing engine)
│   │   │   │   │   │   ├── navigation/           # Navigation management
│   │   │   │   │   │   │   └── NavigationManager.kt (navigation coordination)
│   │   │   │   │   │   ├── timer/                # Timer management system
│   │   │   │   │   │   │   ├── CheckInTimerManager.kt (230 lines) - Timer control
│   │   │   │   │   │   │   ├── TimerModels.kt (timer data models)
│   │   │   │   │   │   │   └── CLAUDE.md (documentation)
│   │   │   │   │   │   ├── utilities/            # Consolidated utility functions
│   │   │   │   │   │   │   ├── ErrorHandling.kt (centralized error handling)
│   │   │   │   │   │   │   ├── HistoryFormatter.kt (conversation history formatting)
│   │   │   │   │   │   │   ├── HistoryTokenManager.kt (token management)
│   │   │   │   │   │   │   ├── TextFormatting.kt (text processing utilities)
│   │   │   │   │   │   │   └── WishUtilities.kt (299 lines) - Wish processing functions
│   │   │   │   │   │   ├── voice/                # Voice processing components
│   │   │   │   │   │   │   ├── VoiceCommandEntity.kt (voice command processing)
│   │   │   │   │   │   │   ├── VoiceFunctions.kt (voice utility functions)
│   │   │   │   │   │   │   └── VoiceProcessor.kt (voice processing logic)
│   │   │   │   │   │   ├── state/                # State management (if present)
│   │   │   │   │   │   ├── BrainService.kt (551 lines) - LLM integration service
│   │   │   │   │   │   ├── ConversationAgent.kt (1849 lines) - Central orchestrator
│   │   │   │   │   │   ├── AgentCortex.kt (243 lines) - State management hub
│   │   │   │   │   │   ├── AgentClasses.kt (shared data classes and types)
│   │   │   │   │   │   ├── AgentViewModel.kt (agent UI state management)
│   │   │   │   │   │   ├── CoreLoopState.kt (core loop state definitions)
│   │   │   │   │   │   ├── ConceptTools.kt (274 lines) - Concept management utilities
│   │   │   │   │   │   └── SessionContextManager.kt (session context management)
│   │   │   │   │   ├── main/                     # Main UI screens and ViewModels
│   │   │   │   │   │   ├── MainScreen.kt (20KB, 540 lines)
│   │   │   │   │   │   ├── MainViewModel.kt (4.8KB, 137 lines)
│   │   │   │   │   │   ├── ManifestationDetailScreen.kt (4.1KB, 128 lines)
│   │   │   │   │   │   ├── ManifestationDetailViewModel.kt (406B, 15 lines)
│   │   │   │   │   │   └── MainScreenState.kt (736B, 25 lines)
│   │   │   │   │   ├── concept/                  # Concept-related UI and business logic
│   │   │   │   │   │   ├── ConceptScreen.kt (21KB, 481 lines)
│   │   │   │   │   │   └── ConceptViewModel.kt (10KB, 257 lines)
│   │   │   │   │   ├── navigation/               # App navigation and routing
│   │   │   │   │   │   ├── ManifestorNavHost.kt (3.0KB, 80 lines)
│   │   │   │   │   │   └── ManifestorApp.kt (560B, 19 lines)
│   │   │   │   │   ├── basevox/                  # Core voice recognition and TTS infrastructure
│   │   │   │   │   │   ├── VoiceRecognitionRepository.kt (21KB, 494 lines)
│   │   │   │   │   │   ├── VoiceManagedViewModel.kt (8.7KB, 228 lines)
│   │   │   │   │   │   ├── TextToSpeechRepository.kt (4.6KB, 129 lines)
│   │   │   │   │   │   ├── AudioSourceSelector.kt (2.5KB, 71 lines)
│   │   │   │   │   │   ├── SpeechUiComponents.kt (1.9KB, 59 lines)
│   │   │   │   │   │   └── SpeechClient.kt (116B, 6 lines)
│   │   │   │   │   ├── utils/                    # UI utilities
│   │   │   │   │   ├── theme/                    # Theming and styles
│   │   │   │   │   └── statuslog/                # Status logging UI
│   │   │   │   ├── data/                         # Data layer: models, repositories, persistence
│   │   │   │   │   ├── ManifestationDatabase.kt (4.2KB, 117 lines)
│   │   │   │   │   ├── AppContainer.kt (6.2KB, 167 lines)
│   │   │   │   │   ├── ConversationRepository.kt (8.3KB, 240 lines)
│   │   │   │   │   ├── ConceptRepository.kt (5.5KB, 161 lines)
│   │   │   │   │   ├── ManifestationRepository.kt (1.6KB, 54 lines)
│   │   │   │   │   ├── ConversationLogDao.kt (3.8KB, 113 lines)
│   │   │   │   │   ├── ManifestationDao.kt (1.6KB, 54 lines)
│   │   │   │   │   ├── ConceptDao.kt (1.8KB, 57 lines)
│   │   │   │   │   ├── ConversationLog.kt (2.1KB, 60 lines)
│   │   │   │   │   ├── Entities.kt (3.0KB, 110 lines)
│   │   │   │   │   ├── Concepts.kt (6.5KB, 169 lines)
│   │   │   │   │   ├── Enums.kt (718B, 31 lines)
│   │   │   │   │   ├── AppSettings.kt (3.4KB, 100 lines)
│   │   │   │   │   ├── Converters.kt (336B, 17 lines)
│   │   │   │   │   ├── Constants.kt (72B, 4 lines)
│   │   │   │   │   └── ManifestorApplication.kt (1.2KB, 41 lines)
│   │   │   │   ├── AppViewModelProvider.kt
│   │   │   │   ├── MainActivity.kt               # Main Android entry point
│   │   │   │   └── ...
│   │   │   ├── res/                              # Android resources (layouts, drawables, etc.)
│   │   │   └── AndroidManifest.xml
│   │   └── ...
│   ├── build.gradle.kts
│   └── ...
├── context/                                      # Project context documentation
│   ├── codebase_context.md                      # (This file) - Technical implementation overview
│   ├── dialoguechain_context.md                 # DialogueChain system deep-dive
│   └── project_context.md                       # High-level project goals and workflows
├── planning/                                    # Planning documents and PRDs
├── tasks/                                       # Task Master task files
├── .taskmasterconfig                            # Task Master config
├── README.md                                    # Project overview
└── ...
```

### Directory Notes
- **app/src/main/java/com/example/voxmanifestorapp/ui/agent/**: Core dialog system with modular architecture:
  - **affirmations/**: Affirmation processing and UI overlay components
  - **checkin/**: Sophisticated 4-chain dialogue system for check-in conversations
  - **commands/**: Function-based command processing and stateful conversation flows
  - **components/**: Decomposed UI components (formerly AgentUiComponents.kt)
  - **coreloop/**: Core loop processing engine and management
  - **navigation/**: Navigation coordination and management
  - **timer/**: Timer management system for check-in and other timed processes
  - **utilities/**: Consolidated utility functions (error handling, formatting, etc.)
  - **voice/**: Voice processing components and command handling
- **app/src/main/java/com/example/voxmanifestorapp/data/**: Data models, repositories, and persistence logic.
- **app/src/main/java/com/example/voxmanifestorapp/ui/**: UI components, screens, navigation, and utilities.
- **agent/context/**: Documentation files for project and technical context.
- **agent/planning/**: Planning documents, PRDs, and project strategy documents.
- **README.md**: High-level project overview and getting started guide.

---

*This section will be updated as the codebase evolves. See parent task for documentation protocol.* 

## Key Entry Points and Main Flows (Expanded)

### Main Entry Points
- **MainScreen.kt**: The primary composable and UI entry point for the app. It orchestrates all major user interactions, including voice input, wish management, and agent communication. MainScreen integrates the agent, voice, and manifestation management, and is responsible for rendering the main user interface and handling user actions.
- **MainViewModel.kt**: The main state and business logic coordinator for MainScreen. It manages session information, slot selection, agent delegation, and repository access. MainViewModel acts as the bridge between UI events and the underlying data/logic layers.
- **ConversationAgent (ui/agent/ConversationAgent.kt)**: Central controller for dialog flow, voice interaction, and state management.
- **AppViewModelProvider.kt**: Provides ViewModels for UI state management across the app.

### Voice Recognition and Synthesis (basevox/)
- **VoiceRecognitionRepository.kt**: Handles low-level voice input, microphone management, and streaming to the speech-to-text API.
- **TextToSpeechRepository.kt**: Manages text-to-speech output, converting agent responses to audio.
- **VoiceManagedViewModel.kt**: Maintains state for voice recognition and synthesis, exposing it to the UI and MainScreen.
- **AudioSourceSelector.kt, SpeechClient.kt, SpeechUiComponents.kt**: Utilities and UI for selecting audio sources and displaying speech status.

**Flow:**
- MainScreen and AgentToolbar use VoiceManagedViewModel to trigger and monitor voice recognition.
- VoiceRecognitionRepository streams audio, receives transcriptions, and updates state.
- TextToSpeechRepository is called by the agent or UI to play responses.

### MainScreen UI Functionality and Agent UI Components
- **AgentUiComponents.kt**: Provides composables for agent process panels, toolbars, overlays, and conversation history. These components are integrated into MainScreen to allow the user to interact with the agent, view conversation history, and manage wishes/concepts.

**Flow:**
- The app launches into MainScreen, which initializes and binds all major UI and logic components.
- User actions (voice, touch) are routed through MainScreen to MainViewModel, which delegates to the appropriate modules (agent, repositories, etc.).
- UI state is updated via MainViewModel and VoiceManagedViewModel.
- AgentUiComponents display current agent state, process, and conversation context.

### Primary Flows of Control
- **Voice Input Flow**:
  1. User provides voice input via the UI.
  2. Input is processed by Google Cloud Speech-to-Text API.
  3. Transcribed text is routed to the ConversationAgent.
  4. ConversationAgent manages dialog state and invokes BrainService for AI-driven responses.
  5. Generated responses are delivered back to the user via Text-to-Speech.

- **Wish and Concept Management Flow**:
  1. User creates or selects a wish.
  2. System guides user through Present and Desired state conceptualization.
  3. Data is persisted using Room Database via repositories and DAOs.
  4. UI state is managed by ViewModels and exposed using StateFlow.

- **Affirmation Session Flow**:
  1. User selects a wish for affirmation.
  2. System generates personalized affirmations using AI.
  3. Voice guides user through affirmation session.
  4. Session progress is tracked and stored.

### Architectural Relationships
- **MVVM Pattern**: MainActivity and UI components interact with ViewModels, which manage state and business logic.
- **Repository Pattern**: Data access is abstracted through repositories, supporting testability and separation of concerns.
- **State Machine**: ConversationAgent uses a hierarchical state machine to manage dialog flow.

### Reference
- For high-level goals and workflows, see `context/project_context.md`.
- For technical structure and module locations, see earlier sections of this file.
- For documentation distinctions, see `context/index.md`.

--- 

## Core Modules and Their Responsibilities

This section summarizes the main modules in the VoxManifestorApp codebase and their primary responsibilities. For architectural context, see `context/project_context.md`.

### Agent Modules

**Check-In System (ui/agent/checkin/):**
- **DialogueChain.kt** (723 lines): Enhanced 4-chain dialogue architecture, containing:
  - `DialogueChain` interface defining the contract for dialogue processing
  - `CheckInDialogueChain` class implementing the complete 4-chain process:
    - Chain 1: Transition evaluation (should conversation continue or move to next phase?)
    - Chain 1B: Theme extraction (identify themes in user's current message)
    - Chain 2: Strategy selection (theme-aware conversational approach selection)
    - Chain 3: Response generation (theme-aware response crafting)
  - `TransitionEvaluationResult` for comprehensive evaluation results
  - Robust error handling and fallback mechanisms for each chain
- **TransitionChain.kt** (451 lines): Dedicated transition processing system:
  - Handles phase suggestion and message crafting when transition is needed
  - Chain A: Analytical phase selection based on theme-wish correlation
  - Chain B: Creative message generation with reasoning incorporation
  - Theme selection and overload management
- **CheckInSystem.kt** (243 lines): Canonical data classes and enums for check-in state management:
  - `CheckInState`, `CheckInStage` enums for tracking conversation progress
  - `UserEngagementMetrics` for measuring user interaction patterns
  - `Strategy` enum defining 10 conversation strategies (CONVERSATION_STARTING, RAPPORT_BUILDING, etc.)
  - `TransitionDecision`, `StrategySelection`, `Response` data classes for chain outputs
  - `ConversationalTheme` for theme tracking and persistence
  - `CheckInSystem.Prompts` containing system instructions and prompt builders
- **CoachingTranscripts.kt** (701 lines): Strategy-specific conversation examples and training data:
  - Real coaching conversation patterns for AI training
  - `getExamplesForStrategy()` function for prompt enhancement
  - Strategy-specific dialogue examples for all 10 conversation approaches

**Core Agent Files (ui/agent/):**
- **ConversationAgent.kt** (1849 lines): *[REFACTORED]* Central orchestrator for dialog flow:
  - Delegates to specialized modules (commands, coreloop, navigation, etc.)
  - Integrates with DialogueChain and TransitionChain for check-in processing
  - Manages transitions between Core Loop phases via CoreLoopManager
  - Orchestrates voice input/output via BrainService and TTS
  - Significantly reduced in size through modular delegation
- **BrainService.kt** (551 lines): LLM integration service, providing AI capabilities:
  - **Model Configuration**: Gemini 2.0 Flash (default), Gemini 2.0 Flash for check-in responses
  - Google Gemini API integration with structured JSON parsing
  - **DialogueChain Methods**: `getCheckInEvaluation()`, `extractThemesFromCurrentUserMessage()`, `selectConversationStrategyWithThemes()`, `generateCheckInResponse()`
  - **TransitionChain Methods**: `makePhaseSuggestion()`, `craftTransitionMessage()`
  - **Core Methods**: `getCoreLoopGuidance()`, `getNextConceptAction()`, `generateAffirmations()`
  - Prompt engineering utilities and response validation
  - Error handling and fallback mechanisms for AI failures
- **AgentCortex.kt** (243 lines): Single source of truth (SSoT) for agent state:
  - Exposes `checkInState`, `coreLoopState`, `conversationHistory` as `StateFlow`
  - Receives UI intents via `MutableSharedFlow<UiIntent>` processed by `ConversationAgent`
  - Contains `updateActiveThemes()`, `updateEngagementMetrics()` for theme management
  - Provides `progressCheckInStage()` for stage transitions
- **CoreLoopState.kt**: State management for 7-phase core dialog loop
- **ConceptTools.kt** (274 lines): Utilities for concept building and management workflows
- **AgentClasses.kt**: Shared data classes and types for agent logic
- **AgentViewModel.kt**: ViewModel for agent-related UI state
- **SessionContextManager.kt**: Session context management and coordination

**Modular Agent Components (ui/agent/):**

**Commands Module (ui/agent/commands/):** *[FUNCTION-BASED ARCHITECTURE]*
- **CommandFunctions.kt** (277 lines): Discrete command handlers following clean function-based patterns:
  - `handleSelectCommand()`, `handleDefineCommand()`, `handleStartCommand()`, etc.
  - Pure functions with clear input/output contracts
  - No side effects or state mutations
- **CommandMode.kt** (420 lines): Stateful conversation flows for wish collection and selection:
  - Manages complex multi-turn conversations for wish management
  - State machine implementation for conversation progression
  - Integration with ConversationAgent for orchestration

**Components Module (ui/agent/components/):** *[DECOMPOSED UI ARCHITECTURE]*
- **AgentPanels.kt** (404 lines): Panel components for agent interface:
  - Process panels, status displays, and information panels
  - Modular composable components for agent UI
- **AgentProcessPanel.kt** (457 lines): Process visualization and debugging interfaces:
  - Real-time process status and conversation flow visualization
  - Debug information and system status displays
- **AgentToolbar.kt**: Toolbar components for agent interaction controls
- **AgentDisplays.kt**: Display utilities and formatting components
- **AgentUtils.kt**: UI utility functions and helpers
- **CheckInTimer.kt**: Timer UI components for check-in visualization
- **ModeStatusBar.kt**: Status display components for conversation modes

**Core Loop Module (ui/agent/coreloop/):**
- **CoreLoopManager.kt**: Processing engine for Core Loop phases 2-7:
  - Follows "frontal cortex" architecture pattern
  - Performs complex internal processing (LLM integration, algorithms, analysis)
  - Returns CoreLoopOutcome results for ConversationAgent to act upon
  - NO access to external interfaces (speech, state updates, history, UI)

**Navigation Module (ui/agent/navigation/):**
- **NavigationManager.kt**: Navigation coordination and management:
  - Handles screen transitions and conversation flow navigation
  - Maintains navigation state and context preservation

**Timer Module (ui/agent/timer/):**
- **CheckInTimerManager.kt** (230 lines): Timer control and management:
  - 3-minute check-in timer with control methods
  - Timer state integration with AgentCortex
  - Start, stop, addTime, subtractTime, forceTransition functionality
- **TimerModels.kt**: Timer data models and state definitions

**Utilities Module (ui/agent/utilities/):** *[CONSOLIDATED FUNCTIONS]*
- **ErrorHandling.kt**: Centralized error handling functions:
  - Consistent error processing across agent modules
  - Exception handling patterns and fallback mechanisms
- **HistoryFormatter.kt**: Conversation history formatting utilities:
  - Converts conversation history to LLM-readable formats
  - Handles speaker roles and formatting consistency
- **HistoryTokenManager.kt**: Token management for LLM interactions:
  - Ensures conversation history fits within token limits
  - Intelligent truncation with preservation of recent turns
- **TextFormatting.kt**: Text processing utilities and formatting functions
- **WishUtilities.kt** (299 lines): Wish processing and management functions:
  - Wish data processing, validation, and transformation utilities
  - Integration helpers for wish-related operations

**Voice Module (ui/agent/voice/):**
- **VoiceCommandEntity.kt**: Voice command processing and recognition:
  - Command keyword matching and processing
  - Voice command state management
- **VoiceFunctions.kt**: Voice utility functions and processing helpers
- **VoiceProcessor.kt**: Voice processing logic and coordination

**Voice Infrastructure (ui/basevox/):** *[CRITICAL SYSTEM]*
- **VoiceRecognitionRepository.kt** (21KB, 494 lines): *[KEY FILE]* Core voice input processing:
  - Google Cloud Speech-to-Text API integration
  - Real-time audio streaming and transcription
  - Microphone management and audio source selection
  - State management for recognition sessions
- **VoiceManagedViewModel.kt** (8.7KB, 228 lines): Central voice state coordination:
  - Exposes voice recognition and TTS state via StateFlow
  - Coordinates between UI voice controls and underlying repositories
  - Manages voice session lifecycle and error states
- **TextToSpeechRepository.kt** (4.6KB, 129 lines): AI response audio output:
  - Google Cloud Text-to-Speech API integration
  - Audio playback management and queuing
  - Voice synthesis configuration and error handling
- **AudioSourceSelector.kt** (2.5KB, 71 lines): Audio input device management
- **SpeechUiComponents.kt** (1.9KB, 59 lines): Voice interaction UI elements
- **SpeechClient.kt** (116B, 6 lines): Minimal speech API client interface

**Architecture Notes:**
- **Modular Refactoring Complete**: Agent system refactored from monolithic to modular architecture
- **Function-Based Patterns**: Commands module follows clean function-based architecture
- **Component Decomposition**: AgentUiComponents.kt decomposed into focused component modules
- **Utilities Consolidation**: All utility functions organized in dedicated utilities directory
- **Specialized Managers**: CoreLoopManager, NavigationManager, TimerManager for focused responsibilities
- **Check-in Enhancement**: Dual-chain system (DialogueChain + TransitionChain) with theme extraction
- **Error Handling Centralization**: Consolidated error handling patterns across modules
- **State Management**: AgentCortex remains single source of truth with enhanced theme management

### Core App Infrastructure

**System Infrastructure:**
- **ManifestationDatabase.kt** (4.2KB, 117 lines): *[CORE]* Room database configuration:
  - Database setup, migrations, and entity registration
  - Database initialization and configuration management
- **AppContainer.kt** (6.2KB, 167 lines): *[CRITICAL]* Dependency injection container:
  - Application-level dependency management and service instantiation
  - Repository creation and lifecycle management
  - Configuration of database, network, and service dependencies
- **AppViewModelProvider.kt**: *[CORE]* ViewModel factory and lifecycle management:
  - Provides ViewModels to UI components following MVVM pattern
  - Handles ViewModel instantiation with proper dependency injection
  - Coordinates with AppContainer for repository and service access

**Navigation Infrastructure:**
- **ManifestorNavHost.kt** (3.0KB, 80 lines): *[CORE]* App navigation and routing configuration:
  - Defines all app routes and navigation structure
  - Coordinates between different screens and features
  - Manages navigation state and deep linking
- **ManifestorApp.kt** (560B, 19 lines): *[CORE]* Top-level app composable and navigation setup:
  - Root composable that initializes the entire app
  - Integrates navigation, theming, and global app state
  - Entry point for the Compose UI hierarchy

### Data Modules (data/)
- **ConversationRepository.kt** (8.3KB, 240 lines): *[KEY]* Conversation and session management:
  - Conversation history persistence and retrieval
  - Session metadata management and conversation logging
  - Integration between agent system and persistent storage
- **ConceptRepository.kt** (5.5KB, 161 lines): Concept data management and persistence
- **ManifestationRepository.kt** (1.6KB, 54 lines): Manifestation/wish data access
- **ConversationLogDao.kt** (3.8KB, 113 lines): *[COMPLEX]* Conversation data access:
  - Complex queries for conversation history and session management
  - Conversation search, filtering, and aggregation operations
- **ManifestationDao.kt** (1.6KB, 54 lines): Basic manifestation CRUD operations
- **ConceptDao.kt** (1.8KB, 57 lines): Concept data access and queries
- **ConversationLog.kt** (2.1KB, 60 lines): Conversation history data models
- **Entities.kt** (3.0KB, 110 lines): Primary domain entities (Manifestation, StateDescription, etc.)
- **Concepts.kt** (6.5KB, 169 lines): *[COMPLEX]* Concept data structures and relationships
- **Enums.kt** (718B, 31 lines): Shared enums (DataLoadingState, RecognitionState, etc.)
- **AppSettings.kt** (3.4KB, 100 lines): Application settings and preferences (*partial DataStore integration*)
- **Converters.kt** (336B, 17 lines): Room type converters
- **Constants.kt** (72B, 4 lines): Application constants
- **ManifestorApplication.kt** (1.2KB, 41 lines): Application class and initialization

### Main UI Modules (ui/main/)
- **MainScreen.kt** (20KB, 540 lines): *[CENTRAL UI]* Primary app interface and orchestration:
  - Integrates voice input, agent communication, and manifestation management
  - Coordinates between MainViewModel and VoiceManagedViewModel
  - Renders main user interface and handles user interactions
  - Entry point for most user workflows and session management
- **MainViewModel.kt** (4.8KB, 137 lines): Main state and business logic coordinator:
  - Manages session information, slot selection, agent delegation
  - Acts as bridge between UI events and underlying data/logic layers
  - Repository access and manifestation workflow coordination
- **ManifestationDetailScreen.kt** (4.1KB, 128 lines): Detail view for individual manifestations
- **ManifestationDetailViewModel.kt** (406B, 15 lines): Minimal ViewModel for detail screens
- **MainScreenState.kt** (736B, 25 lines): State data classes for main screen

### Concept UI Modules (ui/concept/)
- **ConceptScreen.kt** (21KB, 481 lines): *[COMPLEX UI]* Concept building and management interface:
  - Structured UI for creating and editing user concepts
  - Integration with ConceptViewModel for reactive state management
  - Complex form handling and concept visualization
- **ConceptViewModel.kt** (10KB, 257 lines): Business logic for concept management:
  - Concept creation, editing, and persistence workflows
  - Integration with ConceptRepository and ConceptTools
  - State management for complex concept building forms

### Utility Modules (ui/utils/)
- **SoundPlayer.kt**: Utility for playing sounds in the app.

### Theme Modules (ui/theme/)
- **Theme.kt, Color.kt, Type.kt**: Theming, color palette, and typography for the app.

---

*This section will be updated as modules evolve. For more details, see the relevant source files and `context/project_context.md`.* 

## Architecture Patterns & Data Flow

### Core Architectural Patterns

**MVVM with Reactive State:**
- ViewModels expose state via `StateFlow<T>` for reactive UI updates
- UI Composables observe state and emit events to ViewModels  
- Example: `AgentCortex` exposes `checkInState: StateFlow<CheckInState>`

**Repository Pattern:**
- Data access abstracted through repositories (e.g., `ConversationRepository`)
- Repositories coordinate between ViewModels and DAOs
- Example: `ConversationRepository.addConversationEntry()` → `ConversationLogDao.insert()`

**State Machine (Agent System):**
- `ConversationAgent` manages 7-phase Core Loop state transitions
- `CoreLoopState` enum: CHECK_IN → WISH_COLLECTION → PRESENT_STATE_EXPLORATION → etc.
- Phase transitions controlled by agent logic and user interactions

### Key Data Flow Patterns

**Voice Input Pipeline:**
```
User Voice → VoiceRecognitionRepository → Google Speech-to-Text → 
ConversationAgent → DialogueChain/BrainService → Response → 
TextToSpeechRepository → Audio Output
```

**UI State Updates:**
```  
User Action → ViewModel → Repository → DAO → Database
     ↓             ↓
UI Composable ← StateFlow ← Repository ← Database Change
```

**Agent State Management:**
```
UI Intent → AgentCortex → ConversationAgent → 
CheckInDialogueChain → BrainService → State Update → 
AgentCortex.StateFlow → UI Updates
```

### Integration Protocols

**Cross-Module Communication:**
- Use `StateFlow` for state observation across modules
- Use repository interfaces for data access from ViewModels
- Use dependency injection via `AppContainer` for service access

**Error Handling Pattern:**
- Try-catch with fallback responses in agent logic
- State-based error indication in UI (loading, error, success states)
- Graceful degradation for AI/network failures

### Development Patterns

**File Modification Impact:**
- **UI Changes**: Modify Composable → Update ViewModel if state changes → Update Repository if data changes
- **Data Model Changes**: Update Entity → Update DAO → Update Repository → Update ViewModel → Update UI
- **Agent Logic Changes**: Update ConversationAgent → Consider impact on AgentCortex state → Update UI components if needed
- **Voice Processing Changes**: Update Repository → Update ViewModel → Coordinate with agent system

**Common Integration Points:**
- `AppContainer`: Add new dependencies and repositories
- `AgentCortex`: Central state for agent-related features  
- `MainViewModel`: Coordinate between UI and core systems
- `BrainService`: Add new AI-driven capabilities

---

## Major Data Models and State Management

This section summarizes the key data models, their relationships, and how state is managed across the VoxManifestorApp. For details, see the data module files in `app/src/main/java/com/example/voxmanifestorapp/data/`.

### Main Data Entities
- **Manifestation**: Represents a user's wish/goal, including title, slot, timeframe, notes, and completion status. (`Entities.kt`)
- **StateDescription**: Describes the present or desired state for a manifestation. Linked to Manifestation by foreign key. (`Entities.kt`)
- **EcologyItem, ResourceItem, MilestoneItem**: Additional details for manifestations, such as intentions, consequences, resources, and milestones. Linked to Manifestation. (`Entities.kt`)
- **Concept, ConceptItem**: Knowledge structures for concept building, with parent-child relationships and metadata. (`Concepts.kt`)
- **ConceptEntity, ConceptItemEntity**: Room entities for persisting concepts and their items. (`Concepts.kt`)
- **ConversationLogEntry**: Stores conversation history, including session grouping, timestamps, wish association, phase, speaker, and content. (`ConversationLog.kt`)
- **SessionMetadata, SessionInfo**: Metadata for conversation sessions, used for history and UI display. (`ConversationLog.kt`, `ConversationRepository.kt`)

### Enums and State Classes
- **Enums**: `DataLoadingState`, `RecognitionState`, `StatusColor` for tracking loading, voice recognition, and status display. (`Enums.kt`)
- **ConversationState**: Sealed class for representing conversation progress. (`Enums.kt`)
- **ConceptType, ConceptActionState**: Guide concept building and agent actions. (`Concepts.kt`)

### State Management Patterns
- **Repositories**: Abstract data access and business logic (e.g., `ManifestationRepository`, `ConceptRepository`, `ConversationRepository`).
- **DAOs**: Room Data Access Objects for CRUD operations on entities (e.g., `ManifestationDao`, `ConversationLogDao`, `ConceptDao`).
- **Room Database**: Used for persistent storage of manifestations, concepts, and conversation logs. (`ManifestationDatabase.kt`)
- **ViewModels**: Manage UI state and expose it via `StateFlow` for reactive updates (e.g., `MainViewModel`, `VoiceManagedViewModel`, `AgentViewModel`, `ConceptViewModel`).
- **StateFlow/Flow**: Used throughout repositories and ViewModels for reactive, observable state management.
- **DataStore**: (Planned/partial) For app settings and preferences. (`AppSettings.kt`)

### Data Flow Example
- User action (voice or UI) triggers ViewModel update.
- ViewModel interacts with Repository to fetch or update data.
- Repository uses DAO to access Room database.
- State changes are emitted via `Flow`/`StateFlow` and observed by UI components for real-time updates.

### Feature vs. Domain Data Models
Feature-specific models (such as check-in state and strategy enums) are defined within their feature modules for encapsulation and modularity.
Domain models (such as wishes, concepts, and conversation logs) are defined in the `data/` directory for app-wide use and persistence.
This separation ensures clean architecture, easier refactoring, and clear ownership of state and logic.

---

*This section will be updated as data models and state management evolve. For more details, see the relevant source files in the data module.* 

## External Dependencies and Integrations

This section summarizes the major external libraries, APIs, and services integrated into VoxManifestorApp. For details, see the build files and referenced modules.

### Cloud and AI Services
- **Google Cloud Speech-to-Text API**: Provides real-time speech recognition for voice input. Integrated via `VoiceRecognitionRepository.kt` and related modules.
- **Google Cloud Text-to-Speech API**: Converts agent responses and affirmations to audio for playback. Used in `TextToSpeechRepository.kt`.
- **Google Gemini AI API**: Powers the agent's language model and dialog system, providing advanced response generation and guidance. Integrated in `BrainService.kt`.

### Android and Jetpack Libraries
- **Room Database**: SQLite abstraction for persistent storage of manifestations, concepts, and conversation logs. (`ManifestationDatabase.kt`, DAOs, entities)
- **Jetpack Compose**: Modern UI toolkit for building declarative, reactive user interfaces. (`MainScreen.kt`, UI modules)
- **Jetpack Compose Material3**: Material Design components for Compose UI.
- **AndroidX Compose Navigation**: Navigation framework for Compose-based screens.
- **Kotlin Coroutines & Flow**: Asynchronous and reactive programming throughout repositories and ViewModels.
- **Kotlin Serialization**: For JSON serialization/deserialization, especially for AI and API responses.
- **DataStore**: (Planned/partial) For app settings and user preferences. (`AppSettings.kt`)

### Build and Deployment Tools
- **Gradle with Kotlin DSL**: Build system and dependency management.
- **Google Cloud Secrets**: For managing API keys and sensitive configuration.
- **KSP (Kotlin Symbol Processing)**: Used for code generation, especially with Room.

### Testing Frameworks
- **JUnit**: Unit testing for business logic.
- **Espresso**: UI testing for Android.

### Additional Libraries
- **gRPC**: Used for communication with Google Cloud APIs.
- **Google Auth Library**: For authentication with Google services.

---

*This section will be updated as new dependencies and integrations are added. For more details, see the build files and referenced modules.* 

## Notable Patterns, Conventions, and Anti-Patterns

This section highlights the key architectural and code patterns, conventions, and any anti-patterns or areas for improvement observed in the VoxManifestorApp codebase.

### Architectural Patterns
- **MVVM (Model-View-ViewModel):** Used throughout the app for clear separation of UI, state, and business logic. ViewModels expose state via StateFlow and handle business logic, while Composables render the UI. (`MainViewModel.kt`, `AgentViewModel.kt`, `ConceptViewModel.kt`)
- **Repository Pattern:** Data access is abstracted through repositories, supporting testability and separation of concerns. (`ManifestationRepository.kt`, `ConceptRepository.kt`, `ConversationRepository.kt`)
- **State Machine:** The dialog system (ConversationAgent) uses a hierarchical state machine to manage conversation flow and transitions. (`ConversationAgent.kt`)
- **3-Chain DialogueChain Architecture:** The check-in system uses a modular 3-chain approach orchestrated by `CheckInDialogueChain`, which sequences transition evaluation, strategy selection, and response generation. This architecture provides robust error handling with meaningful fallbacks at each stage. (`DialogueChain.kt`, `CheckInSystem.kt`)

### Code Patterns
- **Prompt Engineering:** Prompts for LLMs are carefully constructed and include explicit instructions, context, and response formats to ensure reliable AI outputs. All prompt logic is centralized in `CheckInSystem.Prompts` and enhanced with coaching examples from `CoachingTranscripts.kt`.
- **Fallback and Error Handling:** Each stage of the DialogueChain (transition, strategy, response) includes robust fallback logic to handle errors or model failures gracefully, ensuring the user experience is never interrupted. (`DialogueChain.kt`)
- **Reactive State Management:** StateFlow and Flow are used for reactive, observable state updates throughout repositories and ViewModels.
- **Composable UI:** Jetpack Compose is used for modular, declarative UI components, with clear separation of concerns and feature-based organization. (`MainScreen.kt`, `AgentUiComponents.kt`)

### Conventions
- **Naming:** Follows Kotlin standards (PascalCase for classes, camelCase for functions/variables, UPPERCASE_WITH_UNDERSCORES for constants). File names match contained classes.
- **File Structure:** Organized by feature and responsibility (e.g., agent, checkin, concept, main, data, utils, theme).
- **Error Handling:** Try-catch blocks, structured error reporting, and state-based error visualization in the UI.
- **Testing:** Unit tests for business logic, UI tests for critical flows, repository tests for data access.

## Operational Paradigms: Two-Mode Architecture

VoxManifestor operates under two distinct paradigms that determine how user input is processed and how the AI brain is accessed. Understanding this dual-mode architecture is crucial for development and user experience design.

### Idle/Command-Listening Mode

**State Characteristics:**
- `DialogueState`: `Idle` or `ExpectingInput` with `currentConversation == null`
- `ConversationType`: `null`
- `conversationScope`: `null`

**Voice Processing Pipeline:**
```
User Voice → VoiceRecognitionRepository → Keyword Matching → 
Command Processing → Predetermined Response → TTS Output
```

**Command Recognition System:**
- Uses `VoiceCommandEntity.processCommand()` for keyword matching
- Available commands: START, STOP, SELECT, DEFINE, READ, INITIATE, AFFIRM, HELP, QUIT
- Keywords: "manifest", "start", "select", "define", "read", "affirm", etc.
- No AI/Brain Service calls - purely deterministic responses

**Usage Examples:**
- "Start" → Initiates Core Loop conversation
- "Select slot 2" → Opens wish selection interface  
- "Read wishes" → Lists current manifestations
- "Help" → Displays available commands

### Active Conversation Mode

**State Characteristics:**
- `DialogueState`: `Speaking`, `Thinking`, or `ExpectingInput`
- `ConversationType`: `CoreLoop` or `ConceptBuilding`
- `conversationScope`: Active CoroutineScope managing conversation lifecycle

**Voice Processing Pipeline:**
```
User Voice → VoiceRecognitionRepository → Direct to Brain Processing →
BrainService/DialogueChain → Context-Aware AI Response → TTS Output
```

**AI Integration Patterns:**
- **ConceptBuilding**: `getNextBrainDecision()` → `BrainService.getNextConceptAction()`
- **CoreLoop**: `handleCoreLoopResponse()` → `CheckInDialogueChain` → Multi-chain AI processing
- All speech treated as conversational content, keywords ignored (except "stop")
- Maintains conversation history and contextual state

**Usage Examples:**
- Extended voice journaling during check-in
- Natural conversation about wishes and goals
- Multi-turn dialogue for concept exploration

### Mode Switching: toggleBrainConversation()

**Transition Mechanism:**
- **Idle → Active**: Triggered by START command or UI interaction
- **Active → Idle**: Triggered by UI stop button, conversation completion, or timeout
- **State Management**: Manages `conversationScope` lifecycle, resets dialogue state
- **Cleanup**: Cancels ongoing AI calls, resets conversation history, stops timers

**Critical Implementation Details:**
- `conversationScope` provides clean conversation lifecycle management
- `VoiceCommandEntity.STOP` only processed in Idle mode (when `currentConversation == null`)
- Mode switching is atomic with `AtomicBoolean` to prevent race conditions
- Network errors trigger automatic return to Idle mode

### Brain Access Patterns

**Idle Mode - No Direct Brain Access:**
- Command processing through simple keyword matching
- Predetermined responses based on command type
- No LLM API calls or complex AI processing
- Efficient for quick administrative actions

**Active Mode - Full Brain Integration:**
- All user input routed to `BrainService` or specialized AI systems
- Context-aware processing with conversation history
- Sophisticated prompt engineering and response generation
- Dynamic conversation flow based on AI decisions

### Integration with Check-In System

The Check-In system represents the most sophisticated implementation of Active Conversation Mode:
- **Multi-Chain Processing**: Transition evaluation → Strategy selection → Response generation
- **Theme Extraction**: AI identifies conversational themes for context preservation
- **Strategy-Based Responses**: 8 different conversation strategies (RAPPORT_BUILDING, EMPATHETIC_LISTENING, etc.)
- **Robust Error Handling**: Graceful fallbacks ensure conversation continuity

### Resolved Architecture Issues
- **✅ Large File Decomposition:** ConversationAgent reduced from ~3000 to 1849 lines through modular delegation
- **✅ UI Component Breakdown:** AgentUiComponents.kt decomposed into focused component modules
- **✅ Utilities Organization:** All utility functions consolidated in dedicated utilities directory
- **✅ Function-Based Architecture:** Commands module implements clean function-based patterns
- **✅ Specialized Managers:** CoreLoopManager, NavigationManager provide focused responsibilities

### Remaining Areas for Improvement
- **Command Mode Enhancement:** Voice command vocabulary could be expanded for more administrative features
- **Partial DataStore Integration:** App settings and preferences are only partially implemented
- **Theme UI Integration:** Conversational themes are extracted but not yet displayed to users
- **Core Loop Integration:** ConceptBuilding flow integration into Core Loop phases still in progress

### Current Architecture Strengths
- **Modular Architecture:** Clean separation of concerns with specialized modules
- **Function-Based Patterns:** Commands module demonstrates clean, testable function-based design
- **Dual-Chain Processing:** DialogueChain + TransitionChain provides sophisticated conversation management
- **Theme Extraction:** Advanced theme identification and persistence across conversation turns
- **Centralized Error Handling:** Consistent error processing across all agent modules
- **Utilities Consolidation:** All utility functions organized following user preferences
- **State Management:** AgentCortex maintains single source of truth with enhanced capabilities

---

*This section will be updated as new patterns emerge or anti-patterns are addressed. For more details, see the referenced files and architectural documentation.* 

## Check-In System Integration

The check-in system implements a sophisticated 3-chain AI-driven conversation architecture as **Phase 1** of VoxManifestor's Core Loop. It serves as the conversational entry point for each session, facilitating voice journaling and determining optimal transitions to subsequent phases.

### Architecture Overview
- **DialogueChain.kt** (13KB, 335 lines): Complete 3-chain implementation (transition evaluation → strategy selection → response generation)
- **CheckInSystem.kt** (4.5KB, 119 lines): Canonical data types, state management, and prompts
- **CoachingTranscripts.kt** (33KB, 519 lines): Strategy-specific AI training examples
- **Integration**: Orchestrated by `ConversationAgent.progressCheckIn()`, state managed via `AgentCortex`

### Current Status
**Implemented:** Complete 3-chain architecture, 8 conversation strategies, robust error handling, Google Gemini integration

**Blocking MVP:** Check-In State Synchronization issue between `AgentCortex` and `DialogueChain` systems

**Detailed Technical Documentation:** See `context/dialoguechain_context.md` for comprehensive architecture details, data flows, integration points, and known issues.

## Development Guidelines

### Quick Start for Common Tasks

**Adding New UI Screens:**
1. Create Composable in appropriate `ui/` subdirectory  
2. Create ViewModel with `StateFlow` for reactive state
3. Add navigation route in `ManifestorNavHost.kt`
4. Register ViewModel in `AppViewModelProvider.kt` if needed

**Adding Agent Capabilities:**
1. Add new prompts to `BrainService.kt` with error handling
2. Create specialized manager in appropriate module (coreloop/, navigation/, etc.)
3. Update `ConversationAgent.kt` to delegate to new manager
4. Add state tracking to `AgentCortex.kt` if needed
5. Create UI components in `components/` directory with focused responsibility

**Modifying Database Schema:**
1. Update entities in `data/Entities.kt` or related files
2. Add/modify DAO methods in appropriate DAO files
3. Update Repository to expose new data access patterns
4. Create database migration in `ManifestationDatabase.kt`

**Adding Voice Commands:**
1. Add new command functions to `commands/CommandFunctions.kt` following function-based patterns
2. Update `voice/VoiceCommandEntity.kt` with new command types
3. Update voice recognition logic in `VoiceRecognitionRepository.kt`
4. Add command routing in `ConversationAgent.kt` delegation to commands module

### Testing Patterns

**Unit Testing:**
- Test ViewModels with `kotlinx-coroutines-test`
- Mock repositories using test doubles
- Test agent logic with mock `BrainService` responses

**Integration Testing:**
- Test full data flow: UI → ViewModel → Repository → DAO
- Test voice pipeline with mock speech services
- Test agent state transitions with mock AI responses

### Code Quality Guidelines

**File Size Thresholds:**
- **>1000 lines**: Consider modular decomposition (ConversationAgent.kt successfully reduced to 1849 lines)
- **>500 lines**: Review for single responsibility violations and potential module extraction
- **>200 lines**: Good size for most files, consider function-based patterns for utilities

**Reactive State Patterns:**
- Always use `StateFlow` for observable state in ViewModels
- Use `Flow` for data streams from repositories
- Avoid mutable state exposure - use private `MutableStateFlow` with public `StateFlow`

**Error Handling:**
- Provide fallback responses in agent logic
- Use sealed classes for complex state representations
- Log errors appropriately but don't crash user experience

### Architecture Constraints

**State Management Rules:**
- `AgentCortex` is single source of truth for agent state
- ViewModels should not directly manipulate database - use repositories
- UI components should only observe state, not contain business logic

**Dependency Guidelines:**
- Register all dependencies in `AppContainer.kt`
- Use constructor injection for testability
- Avoid circular dependencies between modules
