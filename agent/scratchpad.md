# Wish Creation Implementation - Task Breakdown

## Current Task: TransitionChain Analysis and Modification

### Background

Following the logical flow from check-in → TransitionChain → wish creation, we need to analyze and modify the TransitionChain to properly support wish creation while preserving concept screen logic for post-MVP.

### TransitionChain Current State Analysis

**Current Flow:**
1. `DialogueChain.evaluateTransition()` determines transition is needed
2. `TransitionChain.processTransition(themes, enhancedWishes)` called
3. **Chain A**: `makePhaseSuggestion()` - Analyzes themes/wishes, returns `PhaseSuggestionResult`
4. **Chain B**: `craftTransitionMessage()` - Creates spoken message with reasoning
5. Returns `TransitionActionPlan` with `proposedPhase`, `targetWishId`, `actionSuggestion`

**Key Findings:**
- `getValidTransitionPhases()` already includes `WISH_COLLECTION` ✅
- `buildPhaseSuggestionPrompt()` has WISH_COLLECTION criteria defined ✅
- Current prompt emphasizes concept exploration over wish creation ❌
- No mechanism to preserve/stash concept screen logic ❌

### Required Modifications

**1. Phase Suggestion Prompt Updates**
- **Current WISH_COLLECTION criteria**: "Themes suggest new focus areas not covered by existing wishes"
- **Needed**: Emphasize wish creation as primary option for MVP
- **Preserve**: Concept screen logic in comments or separate method for post-MVP

**2. Message Crafting Updates**
- **Current**: Generic transition messages
- **Needed**: Wish creation-specific messaging
- **Preserve**: Concept screen messaging logic

**3. Enhanced Wish Data Integration**
- **Current**: Receives `List<EnhancedWishSummary>` with present/desired state items
- **Needed**: Use basic wish data for wish creation scenarios
- **Preserve**: Enhanced data for concept screen scenarios

### Implementation Plan

**Task 2.1: Analyze Current TransitionChain Prompts [COMPLETED]**
- The original prompt functions have been preserved by commenting them out in TransitionChain.kt (see backup methods at the bottom of the file).
- No edits to the active prompt logic have been made yet.
- Ready to proceed to planning the modifications for MVP-focused wish creation logic (Task 2.2).

**Task 2.2: Plan and Implement MVP-Focused Prompt Modifications [NEXT]**
- Next step: Plan the specific changes needed to update the prompts for wish creation emphasis, slot analysis, and MVP requirements.

## Detailed Analysis: Current TransitionChain Prompts

### Current WISH_COLLECTION Criteria (Lines 228-232)
```
1. **WISH_COLLECTION** - Use when:
   - Themes suggest new focus areas not covered by existing wishes
   - User expresses new aspirations or goals not related to current wishes
   - No existing wishes, or themes don't relate to current wishes
   - Set targetWishId: null
```

**Analysis**:
- ✅ Already prioritizes new focus areas
- ❌ Doesn't emphasize wish creation as primary MVP option
- ❌ Doesn't consider available slots in decision-making

### Current Message Crafting (Lines 320-412)
**Strengths**:
- Comprehensive theme analysis
- Natural conversational flow
- Detailed reasoning incorporation

**Issues for Wish Creation**:
- Generic phase introduction
- No wish creation-specific messaging
- Doesn't mention available slots or wish creation benefits

### Required Modifications

**1. Enhanced WISH_COLLECTION Criteria**
```
1. **WISH_COLLECTION** - Use when (MVP PRIORITY):
   - User has empty wish slots available (check available slots first)
   - Themes suggest new focus areas not covered by existing wishes
   - User expresses new aspirations or goals not related to current wishes
   - No existing wishes, or themes don't relate to current wishes
   - PRIORITY: For MVP, prefer wish creation when slots are available
   - Set targetWishId: null
```

**2. Wish Creation-Specific Message Crafting**
- Add slot availability analysis to message
- Emphasize benefits of creating new wishes
- Use wish creation-focused language
- Preserve concept screen messaging for post-MVP

**3. Slot Availability Integration**
- Modify `buildPhaseSuggestionPrompt()` to include slot analysis
- Update decision criteria to consider available slots
- Ensure `enhancedWishes` data includes slot information

### Implementation Strategy

**Phase 1: Backup Current Logic**
- Create `buildPhaseSuggestionPrompt_Original()` method
- Create `buildMessageCraftingPrompt_Original()` method
- Preserve all concept screen logic for post-MVP restoration

**Phase 2: Create MVP-Focused Versions**
- Modify existing methods with MVP-focused logic
- Add slot availability analysis
- Emphasize wish creation over concept exploration

**Phase 3: Add Configuration Support**
- Add feature flag or configuration to switch between versions
- Enable easy restoration of concept screen logic post-MVP
- Test both versions work correctly

## Next Implementation Steps

**Step 1: Backup and Preserve Current Logic**
```kotlin
// In TransitionChain.kt - preserve original methods
private fun buildPhaseSuggestionPrompt_ConceptFocused(
    themes: List<ConversationalTheme>,
    enhancedWishes: List<EnhancedWishSummary>
): String {
    // Current implementation - preserved for post-MVP
}

private fun buildMessageCraftingPrompt_ConceptFocused(
    themes: List<ConversationalTheme>,
    phaseSuggestion: PhaseSuggestionResult,
    enhancedWishes: List<EnhancedWishSummary>
): String {
    // Current implementation - preserved for post-MVP
}
```

**Step 2: Modify Current Methods for MVP**
- Update `buildPhaseSuggestionPrompt()` with wish creation emphasis
- Update `buildMessageCraftingPrompt()` with slot availability messaging
- Add slot analysis to decision-making logic

**Step 3: Add Slot Availability Analysis**
```kotlin
private fun analyzeSlotAvailability(enhancedWishes: List<EnhancedWishSummary>): String {
    val totalSlots = 5 // MAX_WISH_SLOTS
    val filledSlots = enhancedWishes.size
    val availableSlots = totalSlots - filledSlots

    return when {
        availableSlots == 0 -> "All 5 wish slots are filled"
        availableSlots == 1 -> "1 wish slot available"
        else -> "$availableSlots wish slots available"
    }
}
```

**Step 4: Update Phase Selection Criteria**
- Modify WISH_COLLECTION criteria to prioritize available slots
- Add slot availability to decision-making prompt
- Ensure LLM considers slot availability in phase selection

**Step 5: Update Message Crafting**
- Add wish creation-specific messaging templates
- Include slot availability in transition messages
- Emphasize benefits of creating new wishes

**Ready to proceed with Step 1: Backing up current logic and creating MVP-focused versions?**