package com.example.voxmanifestorapp.data

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

@Database(
    entities = [
        Manifestation::class,
        ConceptEntity::class,
        ConceptItemEntity::class,
        ConversationLogEntry::class
/*
        EcologyItem::class,
        ResourceItem::class,
        StateDescription::class,
        MilestoneItem::class

 */
    ],
    version = 7,
    exportSchema = false
)

@TypeConverters(DateTypeConverter::class, MetadataTypeConverter::class)
abstract class ManifestationDatabase: RoomDatabase() {
    abstract fun manifestationDao(): ManifestationDao
    abstract fun conceptDao(): ConceptDao
    abstract fun conversationLogDao(): ConversationLogDao

    companion object {
        val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL(
                    "ALTER TABLE manifestations ADD COLUMN slot INTEGER NOT NULL DEFAULT 0"
                )
            }
        }
        val MIGRATION_2_3 = object : Migration(2, 3) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // create concepts table
                database.execSQL(
                    """
                    CREATE TABLE IF NOT EXISTS concepts (
                        id INTEGER PRIMARY KEY NOT NULL,
                        name TEXT NOT NULL,
                        type TEXT NOT NULL,
                        manifestationId INTEGER NOT NULL,
                        properties TEXT NOT NULL
                    )
                """
                )

                // Create concept_items table
                database.execSQL(
                    """
                    CREATE TABLE IF NOT EXISTS concept_items (
                        id INTEGER PRIMARY KEY NOT NULL,
                        conceptId INTEGER NOT NULL,
                        content TEXT NOT NULL,
                        position INTEGER NOT NULL,
                        metadata TEXT NOT NULL,
                        FOREIGN KEY (conceptId) REFERENCES concepts (id) ON DELETE CASCADE
                    )
                """
                )
            }
        }
        val MIGRATION_3_4 = object : Migration(3, 4) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // not doing anything because (autogenerate =true) is the only change made for concept entities and items.
            }
        }
        
        val MIGRATION_4_5 = object : Migration(4, 5) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Add lastDiscussedTimestamp column to manifestations table
                database.execSQL(
                    "ALTER TABLE manifestations ADD COLUMN lastDiscussedTimestamp INTEGER"
                )
            }
        }
        
        // migration 5 to 6: add conversation_logs table
        val MIGRATION_5_6 = object : Migration(5, 6) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Create the conversation_logs table
                database.execSQL(
                    """
                    CREATE TABLE IF NOT EXISTS conversation_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        sessionId TEXT NOT NULL,
                        timestamp INTEGER NOT NULL,
                        wishId INTEGER NOT NULL,
                        phase TEXT NOT NULL,
                        speaker TEXT NOT NULL,
                        content TEXT NOT NULL,
                        isVisible INTEGER NOT NULL
                    )
                    """
                )
                
                // Add an index on sessionId for faster retrieval
                database.execSQL(
                    "CREATE INDEX index_conversation_logs_sessionId ON conversation_logs (sessionId)"
                )
                
                // Add an index on wishId for faster retrieval
                database.execSQL(
                    "CREATE INDEX index_conversation_logs_wishId ON conversation_logs (wishId)"
                )
            }
        }
        
        // migration 6 to 7: add metadata column to conversation_logs table
        val MIGRATION_6_7 = object : Migration(6, 7) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Add metadata column to conversation_logs table for theme persistence
                database.execSQL(
                    "ALTER TABLE conversation_logs ADD COLUMN metadata TEXT"
                )
            }
        }
    }
}